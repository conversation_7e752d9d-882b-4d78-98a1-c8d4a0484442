# Audio Excellence Store - Placeholder Images List

## 🖼️ **Images That Need to be Replaced with Actual Product Photos**

### **Currently Using Placeholder Divs (Need Real Product Images):**

1. **Pro-Ject Debut Carbon EVO Turntable**
   - Current: Gray gradient with vinyl record icon
   - Location: Line ~600 in store.html
   - Recommended size: 350x280px
   - Product: High-end turntable with carbon fiber tonearm

2. **Supra Cables Quadrax Bi-Wire Speaker Cable**
   - Current: Dark gradient with plug icon
   - Location: Line ~645 in store.html
   - Recommended size: 350x280px
   - Product: Premium Swedish speaker cables

3. **IsoTek V5 Polaris Power Conditioner**
   - Current: Light gradient with bolt icon
   - Location: Line ~665 in store.html
   - Recommended size: 350x280px
   - Product: Power conditioning unit

4. **Wharfedale Linton Heritage Speakers**
   - Current: Brown gradient with speaker icon
   - Location: Line ~685 in store.html
   - Recommended size: 350x280px
   - Product: Classic British bookshelf speakers

5. **Rotel A14 MKII Integrated Amplifier**
   - Current: Dark gradient with sliders icon
   - Location: Line ~705 in store.html
   - Recommended size: 350x280px
   - Product: Integrated amplifier with digital inputs

6. **Mission QX-2 MKII Bookshelf Speakers**
   - Current: Light gradient with music icon
   - Location: Line ~725 in store.html
   - Recommended size: 350x280px
   - Product: Compact bookshelf speakers

7. **SVS SB-3000 Sealed Subwoofer**
   - Current: Black gradient with drum icon
   - Location: Line ~745 in store.html
   - Recommended size: 350x280px
   - Product: Compact sealed subwoofer

### **Currently Using Existing Images (Good to keep):**

✅ **McIntosh MA352** - Using: `../assets/build2.png`
✅ **KEF R11 Meta** - Using: `../assets/Home-White-Speakers-Gold-Rings-sml.jpg`
✅ **Klipsch Heritage Forte IV** - Using: `../assets/build1.png`
✅ **NAD M33** - Using: `../assets/audio-system.jpeg`
✅ **Bluesound Node** - Using: `../assets/Home-Pink-KEF-Muo-Wireless-Speaker-sml.jpg`

## 📝 **How to Replace Placeholder Images:**

1. **Find the placeholder div** in the HTML (search for `<div style="height: 100%; background: linear-gradient`)
2. **Replace the entire div** with an `<img>` tag like this:
   ```html
   <img src="../assets/your-product-image.jpg" alt="Product Name" loading="lazy">
   ```
3. **Add the product badge** if needed:
   ```html
   <div class="product-badge">Badge Text</div>
   ```

## 🎨 **Image Requirements:**

- **Dimensions**: 350x280px (or similar aspect ratio)
- **Format**: JPG or PNG
- **Quality**: High resolution for luxury appeal
- **Background**: Clean, professional product shots
- **Lighting**: Well-lit to show product details
- **Style**: Consistent with luxury brand aesthetic

## 📍 **File Locations to Update:**

- Main store page: `pages/store.html`
- Product modal data: Lines 1070-1145 in store.html
- Asset folder: `assets/` (add new product images here)

## 🔧 **Additional Improvements Needed:**

1. **Add more product data** to the `productData` object for products without modal support
2. **Optimize image loading** with proper lazy loading
3. **Add image hover effects** for better user experience
4. **Consider adding multiple product images** for gallery view in modal

## 💡 **Recommended Product Image Sources:**

- Official manufacturer websites
- High-end audio retailer catalogs
- Professional product photography
- Manufacturer press kits

---

**Note**: All placeholder images are currently using CSS gradients with Font Awesome icons as temporary placeholders. Replace these with actual product photography for the best luxury shopping experience.

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Excellence – Specialists in High End Audio, Home Theatre & Home Automation</title>
    <meta name="description" content="Audio Excellence: Specialists in High End Audio, Home Theatre & Home Automation. 30+ years of passion and expertise you can't get from an online store.">
    <meta name="keywords" content="High End Audio, Home Theatre, Home Automation, Luxury Audio, Audiophile, Cinema, Smart Home, Audio Excellence">
    <meta property="og:title" content="Audio Excellence – High End Audio, Home Theatre & Home Automation">
    <meta property="og:description" content="Experience luxury audio, home theatre, and automation with Audio Excellence. Visit our showroom.">
    <meta property="og:image" content="./image/Header-Home-Parallax.jpg">
    <meta property="og:type" content="website">
    <link rel="icon" type="image/png" href="./assets/logo-full-transparent.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700;800;900&family=Helvetica+Neue:wght@300;400;500;600;700&family=Avenir:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Luxury Color Palette - Inspired by McIntosh & Apple */
            --primary-black: #000000;
            --primary-white: #ffffff;
            --luxury-cream: #f8f6f0;
            --warm-white: #fafafa;
            --accent-copper: #d4a574;
            --accent-gold: #c9a96e;
            --deep-charcoal: #1d1d1f;
            --medium-gray: #86868b;
            --light-gray: #f5f5f7;
            --glass-white: rgba(255, 255, 255, 0.8);
            --glass-black: rgba(0, 0, 0, 0.8);

            /* Shadows & Effects */
            --shadow-luxury: 0 8px 32px rgba(0, 0, 0, 0.12);
            --shadow-copper: 0 4px 20px rgba(212, 165, 116, 0.3);
            --shadow-deep: 0 16px 64px rgba(0, 0, 0, 0.15);
            --border-radius: 12px;
            --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

            /* Typography */
            --font-primary: 'SF Pro Display', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-secondary: 'Helvetica Neue', 'Inter', sans-serif;
            --font-display: 'Playfair Display', serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-primary);
            background: var(--warm-white);
            color: var(--deep-charcoal);
            line-height: 1.6;
            overflow-x: hidden;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Luxury Navigation - Apple/McIntosh Inspired */
        .luxury-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: var(--glass-white);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: var(--transition);
            padding: 0.5rem 0;
        }

        .luxury-navbar.scrolled {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-luxury);
        }

        .navbar-brand {
            font-family: var(--font-display);
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--deep-charcoal) !important;
            text-decoration: none;
            letter-spacing: -0.02em;
        }

        .navbar-nav .nav-link {
            color: var(--deep-charcoal) !important;
            font-weight: 500;
            font-size: 1rem;
            padding: 0.75rem 1.5rem !important;
            border-radius: var(--border-radius);
            transition: var(--transition);
            position: relative;
            margin: 0 0.25rem;
        }

        .navbar-nav .nav-link:hover {
            color: var(--accent-copper) !important;
            background: rgba(212, 165, 116, 0.1);
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            color: var(--accent-copper) !important;
            background: rgba(212, 165, 116, 0.15);
            font-weight: 600;
        }

        .cta-button {
            background: linear-gradient(135deg, var(--accent-copper), var(--accent-gold));
            color: var(--primary-white) !important;
            padding: 0.75rem 2rem !important;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            box-shadow: var(--shadow-copper);
            border: none;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-deep);
            color: var(--primary-white) !important;
            background: linear-gradient(135deg, var(--accent-gold), var(--accent-copper));
        }

        /* Luxury Hero Section */
        .hero-section {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -2;
        }

        .hero-video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.6) 50%, rgba(0,0,0,0.4) 100%);
            z-index: -1;
        }



        .hero-content {
            max-width: 1000px;
            padding: 0 2rem;
            z-index: 2;
            position: relative;
        }

        .hero-title {
            font-family: var(--font-display);
            font-size: clamp(3rem, 6vw, 5rem);
            font-weight: 300;
            line-height: 1.1;
            margin-bottom: 2rem;
            color: var(--primary-white);
            letter-spacing: -0.02em;
        }

        .hero-title .accent {
            color: var(--accent-copper);
            font-weight: 600;
        }

        .hero-subtitle {
            font-size: clamp(1.2rem, 2.5vw, 1.6rem);
            color: var(--primary-white);
            margin-bottom: 3rem;
            font-weight: 400;
            line-height: 1.5;
        }

        .soundwave-container {
            margin: 2rem auto;
            max-width: 600px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Luxury Sections */
        .luxury-section {
            padding: 8rem 0;
            position: relative;
        }

        .luxury-section:nth-child(even) {
            background: var(--warm-white);
        }

        .luxury-section:nth-child(odd) {
            background: var(--luxury-cream);
        }

        .section-title {
            font-family: var(--font-display);
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 300;
            text-align: center;
            margin-bottom: 4rem;
            color: var(--deep-charcoal);
            letter-spacing: -0.02em;
        }

        .section-title .accent {
            color: var(--accent-copper);
            font-weight: 600;
        }

        /* Cards */
        .luxury-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        /* 2x2 Grid for Expertise Section */
        .expertise-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            max-width: 800px;
            margin: 0 auto;
        }

        .luxury-card {
            background: var(--primary-white);
            border: 1px solid rgba(212, 165, 116, 0.2);
            border-radius: var(--border-radius);
            padding: 3rem 2.5rem;
            text-align: center;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-luxury);
        }

        .luxury-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(212, 165, 116, 0.1), transparent);
            transition: left 0.8s ease;
        }

        .luxury-card:hover::before {
            left: 100%;
        }

        .luxury-card:hover {
            transform: translateY(-12px);
            box-shadow: var(--shadow-deep);
            border-color: var(--accent-copper);
        }

        .card-icon {
            font-size: 3.5rem;
            color: var(--accent-copper);
            margin-bottom: 2rem;
            transition: var(--transition);
        }

        .luxury-card:hover .card-icon {
            transform: scale(1.1);
            color: var(--accent-gold);
        }

        .card-title {
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--deep-charcoal);
            font-family: var(--font-display);
        }

        .card-description {
            color: var(--medium-gray);
            line-height: 1.7;
            font-size: 1.1rem;
        }

        /* Brand Carousel */
        .brand-section {
            background: var(--deep-charcoal);
            padding: 6rem 0;
            overflow: hidden;
        }

        .brand-carousel-container {
            position: relative;
            max-width: 100vw;
            margin: 0 auto;
            overflow-x: visible;
        }

        .brand-carousel {
            display: flex;
            white-space: nowrap;
            min-width: 2200px;
            animation: brandScroll 60s linear infinite;
            gap: 4rem;
            align-items: center;
            overflow-x: visible;
        }

        .brand-carousel:hover {
            animation-play-state: paused;
        }

        .brand-logo {
            height: 80px;
            width: auto;
            min-width: 120px;
            filter: grayscale(100%) brightness(1.5);
            transition: var(--transition);
            opacity: 0.7;
            flex-shrink: 0;
        }

        .brand-logo:hover {
            filter: none;
            opacity: 1;
            transform: scale(1.15);
        }

        @keyframes brandScroll {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-50%);
            }
        }

        /* Duplicate brands for seamless loop */
        .brand-carousel::after {
            content: '';
            display: flex;
            gap: 4rem;
            align-items: center;
        }

        /* Footer */
        .luxury-footer {
            background: var(--deep-charcoal);
            padding: 4rem 0 3rem;
            text-align: center;
            border-top: 1px solid rgba(212, 165, 116, 0.2);
        }

        .footer-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .footer-title {
            font-family: var(--font-display);
            font-size: 2rem;
            color: var(--accent-copper);
            margin-bottom: 1.5rem;
            font-weight: 600;
        }

        .footer-contact {
            color: var(--light-gray);
            margin-bottom: 2rem;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .footer-social {
            margin-bottom: 2rem;
        }

        .footer-social a {
            color: var(--accent-copper);
            font-size: 1.75rem;
            margin: 0 1.5rem;
            transition: var(--transition);
        }

        .footer-social a:hover {
            color: var(--primary-white);
            transform: translateY(-3px) scale(1.1);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .luxury-cards {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .expertise-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .brand-grid {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 1.5rem;
            }

            .luxury-section {
                padding: 4rem 0;
            }

            .navbar-nav .nav-link {
                padding: 0.5rem 1rem !important;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }
    </style>
</head>

<body>
    <!-- Modern Navigation -->
    <nav class="navbar navbar-expand-lg luxury-navbar">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <img src="./assets/logo-full-transparent.png" alt="Audio Excellence Logo" style="height: 2.5rem; width: auto; max-width: 220px; object-fit: contain; display: inline-block; vertical-align: middle;" />
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="./index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./pages/music-systems.html">Music Systems</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./pages/home-theatre.html">Home Theatre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./pages/home-automation.html">Home Automation</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="./pages/showcase.html">Showcase</a>
                    </li>
                </ul>
                <a href="./pages/contact.html" class="cta-button ms-3">Book a Demo</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section" id="hero">
        <video class="hero-video" autoplay muted loop>
            <source src="./hero/index page video.mp4" type="video/mp4">
        </video>
        <div class="hero-video-overlay"></div>
        <div class="hero-content" data-aos="fade-up" data-aos-duration="1200">
            <h1 class="hero-title">Specialists in <span class="accent">High End Audio</span>, Home Theatre & Home Automation</h1>
            <p class="hero-subtitle">30+ years of passion and expertise you can't get from an online store</p>

            <!-- Animated Soundwave -->
            <div class="soundwave-container" id="soundwave-hero">
                <svg width="100%" height="80" viewBox="0 0 600 80">
                    <g fill="none" stroke="#FF4A00" stroke-width="3" stroke-linecap="round">
                        <line x1="50" y1="40" x2="50" y2="40">
                            <animate attributeName="y2" values="40;10;40" dur="1.5s" repeatCount="indefinite"/>
                        </line>
                        <line x1="80" y1="40" x2="80" y2="40">
                            <animate attributeName="y2" values="40;20;40" dur="1.3s" repeatCount="indefinite" begin="0.1s"/>
                        </line>
                        <line x1="110" y1="40" x2="110" y2="40">
                            <animate attributeName="y2" values="40;5;40" dur="1.7s" repeatCount="indefinite" begin="0.2s"/>
                        </line>
                        <line x1="140" y1="40" x2="140" y2="40">
                            <animate attributeName="y2" values="40;25;40" dur="1.4s" repeatCount="indefinite" begin="0.3s"/>
                        </line>
                        <line x1="170" y1="40" x2="170" y2="40">
                            <animate attributeName="y2" values="40;15;40" dur="1.6s" repeatCount="indefinite" begin="0.4s"/>
                        </line>
                        <line x1="200" y1="40" x2="200" y2="40">
                            <animate attributeName="y2" values="40;8;40" dur="1.2s" repeatCount="indefinite" begin="0.5s"/>
                        </line>
                        <line x1="230" y1="40" x2="230" y2="40">
                            <animate attributeName="y2" values="40;30;40" dur="1.8s" repeatCount="indefinite" begin="0.6s"/>
                        </line>
                        <line x1="260" y1="40" x2="260" y2="40">
                            <animate attributeName="y2" values="40;12;40" dur="1.5s" repeatCount="indefinite" begin="0.7s"/>
                        </line>
                        <line x1="290" y1="40" x2="290" y2="40">
                            <animate attributeName="y2" values="40;18;40" dur="1.3s" repeatCount="indefinite" begin="0.8s"/>
                        </line>
                        <line x1="320" y1="40" x2="320" y2="40">
                            <animate attributeName="y2" values="40;6;40" dur="1.7s" repeatCount="indefinite" begin="0.9s"/>
                        </line>
                        <line x1="350" y1="40" x2="350" y2="40">
                            <animate attributeName="y2" values="40;22;40" dur="1.4s" repeatCount="indefinite" begin="1.0s"/>
                        </line>
                        <line x1="380" y1="40" x2="380" y2="40">
                            <animate attributeName="y2" values="40;14;40" dur="1.6s" repeatCount="indefinite" begin="1.1s"/>
                        </line>
                        <line x1="410" y1="40" x2="410" y2="40">
                            <animate attributeName="y2" values="40;28;40" dur="1.2s" repeatCount="indefinite" begin="1.2s"/>
                        </line>
                        <line x1="440" y1="40" x2="440" y2="40">
                            <animate attributeName="y2" values="40;16;40" dur="1.8s" repeatCount="indefinite" begin="1.3s"/>
                        </line>
                        <line x1="470" y1="40" x2="470" y2="40">
                            <animate attributeName="y2" values="40;10;40" dur="1.5s" repeatCount="indefinite" begin="1.4s"/>
                        </line>
                        <line x1="500" y1="40" x2="500" y2="40">
                            <animate attributeName="y2" values="40;24;40" dur="1.3s" repeatCount="indefinite" begin="1.5s"/>
                        </line>
                        <line x1="530" y1="40" x2="530" y2="40">
                            <animate attributeName="y2" values="40;12;40" dur="1.7s" repeatCount="indefinite" begin="1.6s"/>
                        </line>
                    </g>
                </svg>
            </div>

            <!-- View Showcase Button -->
            <div class="mt-4" data-aos="fade-up" data-aos-delay="800">
                <a href="./pages/showcase.html" class="cta-button" style="font-size: 1.1rem; padding: 1rem 2.5rem;">
                    <i class="fas fa-crown me-2"></i>View Showcase
                </a>
            </div>
        </div>
    </section>



    <!-- Our Expertise Section -->
    <section class="luxury-section" id="expertise">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Our <span class="accent">Expertise</span></h2>
            <div class="expertise-grid px-3">
                <div class="luxury-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-icon">
                        <i class="fas fa-music"></i>
                    </div>
                    <h3 class="card-title">High-End Audio</h3>
                    <p class="card-description">With our specially selected music playback systems, you can relive the music of your favourite bands and artists.</p>
                </div>

                <div class="luxury-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-icon">
                        <i class="fas fa-film"></i>
                    </div>
                    <h3 class="card-title">Home Theatre</h3>
                    <p class="card-description">Bring the thrill of the Silver Screen into your home with your own private cinema.</p>
                </div>

                <div class="luxury-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3 class="card-title">Home Automation</h3>
                    <p class="card-description">Add comfort, convenience, security, and energy efficiency to your lifestyle.</p>
                </div>

                <div class="luxury-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="card-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <h3 class="card-title">Office & Hospitality</h3>
                    <p class="card-description">AV and control systems for boardrooms, office spaces, and restaurants.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Customised Solutions -->
    <section class="luxury-section" id="solutions">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Customised <span class="accent">Solutions</span></h2>
            <div class="luxury-cards px-3">
                <div class="luxury-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3 class="card-title">Tailored Design</h3>
                    <p class="card-description">Every system is designed for your space and needs. Modern layouts with premium finishes.</p>
                </div>

                <div class="luxury-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="card-title">Security & Reliability</h3>
                    <p class="card-description">We use only the most reliable, high-quality products and installation practices.</p>
                </div>

                <div class="luxury-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3 class="card-title">Smart Integration</h3>
                    <p class="card-description">Seamless integration of lighting, AV, security, blinds, and more.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Some Of Our Builds Section -->
    <section class="luxury-section" id="our-builds" style="padding-bottom: 0; margin-bottom: 2.5rem;">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Some Of <span class="accent">Our Builds</span></h2>
            <div class="builds-flex" style="display: flex; gap: 2rem; justify-content: center; align-items: stretch; flex-wrap: wrap;">
                <!-- Build 1 -->
                <div class="build-card" style="background: var(--primary-white); border: 1px solid rgba(212, 165, 116, 0.2); border-radius: var(--border-radius); box-shadow: var(--shadow-luxury); flex: 1 1 400px; min-width: 320px; max-width: 600px; display: flex; flex-direction: column; align-items: center; padding: 2.5rem 2rem; text-align: center; justify-content: flex-start;">
                    <img src="./assets/build1.png" alt="Build 1 - Klipsch Home Theatre" style="width: 100%; max-width: 480px; height: 320px; border-radius: 10px; margin-bottom: 2rem; object-fit: cover; box-shadow: 0 4px 24px rgba(0,0,0,0.08);">
                    <h3 class="card-title" style="font-size: 2rem; font-family: var(--font-display); font-weight: 600; margin-bottom: 1rem; color: var(--deep-charcoal);">Klipsch 7.1.4 Home Theatre</h3>
                    <p class="card-description" style="color: var(--medium-gray); font-size: 1.15rem;">A fully immersive Dolby Atmos home theatre system featuring Klipsch speakers in a 7.1.4 configuration. Designed for cinematic sound and powerful bass, this build delivers an unforgettable movie experience in your own home.</p>
                </div>
                <!-- Build 2 -->
                <div class="build-card" style="background: var(--primary-white); border: 1px solid rgba(212, 165, 116, 0.2); border-radius: var(--border-radius); box-shadow: var(--shadow-luxury); flex: 1 1 400px; min-width: 320px; max-width: 600px; display: flex; flex-direction: column; align-items: center; padding: 2.5rem 2rem; text-align: center; justify-content: flex-start;">
                    <img src="./assets/build2.png" alt="Build 2 - NAD M33 Streaming Amplifier & KEF R11 Speakers" style="width: 100%; max-width: 480px; height: 320px; border-radius: 10px; margin-bottom: 2rem; object-fit: cover; box-shadow: 0 4px 24px rgba(0,0,0,0.08);">
                    <h3 class="card-title" style="font-size: 2rem; font-family: var(--font-display); font-weight: 600; margin-bottom: 1rem; color: var(--deep-charcoal);">NAD M33 Streaming Amplifier<br>KEF R11 Speakers System</h3>
                    <p class="card-description" style="color: var(--medium-gray); font-size: 1.15rem;">A reference-grade two-channel system for music lovers, featuring the NAD M33 streaming amplifier, KEF R11 floorstanding speakers, Supra Quadrax bi-wire speaker cable, and Isotek V5 Polaris power conditioner. This build delivers pure, detailed sound with cutting-edge streaming and audiophile-grade components.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Quality Promise -->
    <section class="luxury-section" id="quality">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Highest Quality <span class="accent">Products</span></h2>
            <div class="luxury-cards px-3">
                <div class="luxury-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <h3 class="card-title">Award-Winning Brands</h3>
                    <p class="card-description">We partner with the world's most respected audio and automation brands.</p>
                </div>

                <div class="luxury-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-icon">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <h3 class="card-title">Certified Installers</h3>
                    <p class="card-description">Our team is factory-trained and certified for every product we install.</p>
                </div>

                <div class="luxury-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3 class="card-title">Unmatched Support</h3>
                    <p class="card-description">We provide ongoing support and service for every system we deliver.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Showroom Invitation -->
    <section class="luxury-section" id="showroom" style="background: linear-gradient(135deg, var(--luxury-cream), var(--warm-white)); text-align: center;">
        <div class="container">
            <div data-aos="zoom-in" style="max-width: 900px; margin: 0 auto; padding: 4rem 3rem; background: var(--primary-white); border-radius: var(--border-radius); border: 1px solid var(--accent-copper); box-shadow: var(--shadow-deep);">
                <h2 style="font-family: var(--font-display); font-size: 3rem; color: var(--deep-charcoal); margin-bottom: 1.5rem; font-weight: 300;">Experience the <span style="color: var(--accent-copper); font-weight: 600;">Difference</span></h2>
                <p style="font-size: 1.4rem; color: var(--medium-gray); margin-bottom: 2.5rem; line-height: 1.6;">This is how music & movies should be experienced…</p>
                <a href="./pages/contact.html" class="cta-button" style="font-size: 1.3rem; padding: 1.2rem 3.5rem;">Visit Our Showroom</a>
            </div>
        </div>
    </section>

    <!-- Brand Partners -->
    <section class="brand-section" id="brands">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up"> <span class="accent">Our Premium Partners</span></h2>
            <div class="brand-carousel-container" data-aos="fade-up" data-aos-delay="200">
                <div class="brand-carousel">
                    <!-- All brands, first set -->
                    <img src="./brands/1.png" alt="Bluesound" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/2.png" alt="Clearaudio" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/3.png" alt="Conrad Johnson" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/4.png" alt="Control4" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/5.png" alt="Crestron" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/6.png" alt="Epson" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/7.png" alt="Golden Ear Tech" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/8.png" alt="Isotek" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/9.png" alt="KEF" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/10.png" alt="Klipsch" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/11.png" alt="Magico" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/12.png" alt="McIntosh" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/13.png" alt="Mission" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/14.png" alt="NAD" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/15.png" alt="Primare" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/16.png" alt="Pro-Ject" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/17.png" alt="Rotel" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/18.png" alt="Supra Cables" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/19.png" alt="SVS Sound Revolution" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/20.png" alt="Wharfedale" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/21.png" alt="Yamaha" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <!-- All brands, duplicate set for seamless loop -->
                    <img src="./brands/1.png" alt="Bluesound" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/2.png" alt="Clearaudio" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/3.png" alt="Conrad Johnson" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/4.png" alt="Control4" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/5.png" alt="Crestron" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/6.png" alt="Epson" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/7.png" alt="Golden Ear Tech" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/8.png" alt="Isotek" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/9.png" alt="KEF" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/10.png" alt="Klipsch" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/11.png" alt="Magico" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/12.png" alt="McIntosh" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/13.png" alt="Mission" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/14.png" alt="NAD" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/15.png" alt="Primare" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/16.png" alt="Pro-Ject" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/17.png" alt="Rotel" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/18.png" alt="Supra Cables" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/19.png" alt="SVS Sound Revolution" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/20.png" alt="Wharfedale" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">
                    <img src="./brands/21.png" alt="Yamaha" class="brand-logo" loading="lazy" style="background: transparent; mix-blend-mode: multiply;">                     

                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="luxury-footer" id="contact">
        <div class="container">
            <div class="footer-content">
                <h3 class="footer-title">Audio Excellence</h3>
                <p class="footer-contact">
                    Gateway Office Park, 1 Sugar Close, Umhlanga Ridge, 4320<br>
                    <strong>(*************</strong> |
                    <a href="mailto:<EMAIL>" style="color: var(--accent-copper); text-decoration: none;"><EMAIL></a>
                </p>

                <div class="footer-social">
                    <a href="https://www.facebook.com/audioexcellencesa/" target="_blank" rel="noopener" aria-label="Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://www.instagram.com/excellenceaudio/" target="_blank" rel="noopener" aria-label="Instagram">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>

                <p style="color: var(--light-gray); margin-top: 2rem; font-size: 0.95rem;">
                    © 2025 Audio Excellence Pty Ltd. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.luxury-navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading animation
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in-out';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>

</html>
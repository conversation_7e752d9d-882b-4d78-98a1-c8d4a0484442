<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audiophile Showcase – Audio Excellence</title>
    <meta name="description" content="Experience the pinnacle of audio excellence. Discover why discerning audiophiles choose our reference-grade systems for the ultimate musical journey.">
    <link rel="icon" type="image/png" href="../assets/logo-full-transparent.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700;800;900&family=Helvetica+Neue:wght@300;400;500;600;700&family=Avenir:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            /* Luxury Color Palette */
            --primary-black: #000000;
            --primary-white: #ffffff;
            --luxury-cream: #f8f6f0;
            --warm-white: #fafafa;
            --accent-copper: #d4a574;
            --accent-gold: #c9a96e;
            --deep-charcoal: #1d1d1f;
            --medium-gray: #86868b;
            --light-gray: #f5f5f7;
            --glass-white: rgba(255, 255, 255, 0.8);
            
            /* Effects */
            --shadow-luxury: 0 8px 32px rgba(0, 0, 0, 0.12);
            --shadow-copper: 0 4px 20px rgba(212, 165, 116, 0.3);
            --shadow-deep: 0 16px 64px rgba(0, 0, 0, 0.15);
            --border-radius: 12px;
            --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            
            /* Typography */
            --font-primary: 'SF Pro Display', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-display: 'Playfair Display', serif;
        }
        
        body {
            font-family: var(--font-primary);
            background: var(--warm-white);
            color: var(--deep-charcoal);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
        }
        
        .luxury-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: var(--glass-white);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: var(--transition);
            padding: 0.5rem 0;
        }

        .luxury-navbar.scrolled {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-luxury);
        }

        .navbar-brand {
            font-family: var(--font-display);
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--deep-charcoal) !important;
            text-decoration: none;
            letter-spacing: -0.02em;
        }

        .navbar-nav .nav-link {
            color: var(--deep-charcoal) !important;
            font-weight: 500;
            font-size: 1rem;
            padding: 0.75rem 1.5rem !important;
            border-radius: var(--border-radius);
            transition: var(--transition);
            position: relative;
            margin: 0 0.25rem;
        }

        .navbar-nav .nav-link:hover {
            color: var(--accent-copper) !important;
            background: rgba(212, 165, 116, 0.1);
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            color: var(--accent-copper) !important;
            background: rgba(212, 165, 116, 0.15);
            font-weight: 600;
        }

        .navbar-toggler {
            border: none;
            padding: 0.25rem 0.5rem;
        }

        .navbar-toggler:focus {
            box-shadow: none;
        }

        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2829, 29, 31, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }
        
        .luxury-section {
            padding: 8rem 0;
            background: var(--luxury-cream);
        }
        
        .luxury-section:nth-child(even) {
            background: var(--warm-white);
        }
        
        .section-title {
            font-family: var(--font-display);
            font-size: clamp(2.5rem, 5vw, 4rem);
            color: var(--deep-charcoal);
            text-align: center;
            margin-bottom: 4rem;
            font-weight: 300;
            letter-spacing: -0.02em;
        }
        
        .section-title .accent {
            color: var(--accent-copper);
            font-weight: 600;
        }
        
        .luxury-card {
            background: var(--primary-white);
            border: 1px solid rgba(212, 165, 116, 0.2);
            border-radius: var(--border-radius);
            padding: 3rem 2.5rem;
            transition: var(--transition);
            height: 100%;
            box-shadow: var(--shadow-luxury);
        }
        
        .luxury-card:hover {
            transform: translateY(-12px);
            border-color: var(--accent-copper);
            box-shadow: var(--shadow-deep);
        }
        
        .hero-showcase {
            background: linear-gradient(135deg, var(--deep-charcoal) 0%, #2a2a2a 100%);
            color: var(--primary-white);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-showcase::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('../assets/Header-Home-Parallax.jpg') center/cover no-repeat;
            opacity: 0.15;
            z-index: 1;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .testimonial-card {
            background: var(--primary-white);
            border-left: 4px solid var(--accent-copper);
            padding: 2.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-luxury);
            margin: 2rem 0;
        }
        

        
        .cta-button {
            background: linear-gradient(135deg, var(--accent-copper), var(--accent-gold));
            color: var(--primary-white) !important;
            padding: 0.75rem 2rem !important;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            box-shadow: var(--shadow-copper);
            border: none;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-deep);
            color: var(--primary-white) !important;
            background: linear-gradient(135deg, var(--accent-gold), var(--accent-copper));
        }
        
        .luxury-footer {
            background: var(--deep-charcoal);
            padding: 4rem 0 3rem;
            text-align: center;
            border-top: 1px solid rgba(212, 165, 116, 0.2);
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--accent-copper);
            font-family: var(--font-display);
        }
        
        .exclusive-badge {
            background: var(--accent-copper);
            color: var(--primary-white);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1rem;
        }

        /* A/B Audio Testing Styles */
        .audio-testing-section {
            background: linear-gradient(135deg, var(--deep-charcoal), #1a1a1a);
            color: var(--primary-white);
            padding: 6rem 0;
            position: relative;
            overflow: hidden;
        }

        .audio-testing-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(212,165,116,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .testing-badge {
            background: linear-gradient(135deg, var(--accent-copper), var(--accent-gold));
            color: var(--primary-white);
            padding: 0.6rem 2rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 700;
            letter-spacing: 1px;
            display: inline-block;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(212, 165, 116, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .audio-comparison-container {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .comparison-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .audio-warning {
            background: rgba(212, 165, 116, 0.1);
            border: 1px solid var(--accent-copper);
            border-radius: 50px;
            padding: 1rem 2rem;
            display: inline-flex;
            align-items: center;
            gap: 1rem;
            color: var(--accent-copper);
            font-weight: 500;
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(212, 165, 116, 0.2); }
            to { box-shadow: 0 0 30px rgba(212, 165, 116, 0.4); }
        }

        .audio-warning i {
            font-size: 1.2rem;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-5px); }
            60% { transform: translateY(-3px); }
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 3rem;
            align-items: center;
            margin-bottom: 4rem;
        }

        .audio-option {
            position: relative;
        }

        .audio-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 3rem 2rem;
            text-align: center;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .audio-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.6s;
        }

        .audio-card:hover::before {
            left: 100%;
        }

        .audio-card.premium {
            border-color: var(--accent-copper);
            background: linear-gradient(135deg, rgba(212, 165, 116, 0.1), rgba(255, 255, 255, 0.05));
            box-shadow: 0 20px 60px rgba(212, 165, 116, 0.2);
        }

        .premium-badge {
            position: absolute;
            top: -10px;
            right: 20px;
            background: linear-gradient(135deg, var(--accent-copper), var(--accent-gold));
            color: var(--primary-white);
            padding: 0.5rem 1.5rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 700;
            letter-spacing: 1px;
            box-shadow: 0 5px 20px rgba(212, 165, 116, 0.4);
        }

        .audio-icon-container {
            position: relative;
            margin-bottom: 2rem;
        }

        .audio-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            position: relative;
            z-index: 2;
        }

        .audio-icon.regular {
            background: linear-gradient(135deg, #666, #888);
            color: var(--primary-white);
        }

        .audio-icon.premium {
            background: linear-gradient(135deg, var(--accent-copper), var(--accent-gold));
            color: var(--primary-white);
            box-shadow: 0 10px 30px rgba(212, 165, 116, 0.4);
        }

        .audio-waves {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            gap: 3px;
            z-index: 1;
        }

        .audio-waves .wave {
            width: 3px;
            background: currentColor;
            border-radius: 2px;
            animation: audioWave 1.5s ease-in-out infinite;
        }

        .audio-waves.regular .wave {
            color: rgba(255, 255, 255, 0.3);
        }

        .audio-waves.premium .wave {
            color: var(--accent-copper);
        }

        .audio-waves .wave:nth-child(1) { height: 20px; animation-delay: 0s; }
        .audio-waves .wave:nth-child(2) { height: 35px; animation-delay: 0.1s; }
        .audio-waves .wave:nth-child(3) { height: 50px; animation-delay: 0.2s; }
        .audio-waves .wave:nth-child(4) { height: 35px; animation-delay: 0.3s; }
        .audio-waves .wave:nth-child(5) { height: 25px; animation-delay: 0.4s; }
        .audio-waves .wave:nth-child(6) { height: 40px; animation-delay: 0.5s; }

        @keyframes audioWave {
            0%, 100% { transform: scaleY(0.3); opacity: 0.7; }
            50% { transform: scaleY(1); opacity: 1; }
        }

        .audio-card h3 {
            color: var(--primary-white);
            font-family: var(--font-display);
            font-size: 1.8rem;
            margin-bottom: 1rem;
            font-weight: 300;
        }

        .audio-description {
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        .audio-specs {
            margin-bottom: 2.5rem;
        }

        .spec-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .spec-item:last-child {
            border-bottom: none;
        }

        .spec-label {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }

        .spec-value {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .spec-value.poor {
            color: #ff6b6b;
        }

        .spec-value.premium {
            color: var(--accent-copper);
        }

        .play-button {
            width: 100%;
            background: transparent;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50px;
            padding: 1.2rem 2rem;
            color: var(--primary-white);
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .play-button.regular:hover {
            border-color: #888;
            background: rgba(136, 136, 136, 0.1);
            transform: translateY(-2px);
        }

        .play-button.premium {
            border-color: var(--accent-copper);
        }

        .play-button.premium:hover {
            border-color: var(--accent-gold);
            background: linear-gradient(135deg, rgba(212, 165, 116, 0.2), rgba(255, 215, 0, 0.1));
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(212, 165, 116, 0.3);
        }

        .play-button.playing {
            animation: playingPulse 1.5s ease-in-out infinite;
        }

        @keyframes playingPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        .play-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--accent-copper), var(--accent-gold));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .play-button.regular .play-icon {
            background: linear-gradient(135deg, #666, #888);
        }

        .button-waves {
            display: flex;
            gap: 2px;
            margin-left: auto;
        }

        .button-waves .wave {
            width: 2px;
            background: currentColor;
            border-radius: 1px;
            animation: buttonWave 1.2s ease-in-out infinite;
            opacity: 0.7;
        }

        .button-waves .wave:nth-child(1) { height: 8px; animation-delay: 0s; }
        .button-waves .wave:nth-child(2) { height: 12px; animation-delay: 0.1s; }
        .button-waves .wave:nth-child(3) { height: 16px; animation-delay: 0.2s; }
        .button-waves .wave:nth-child(4) { height: 12px; animation-delay: 0.3s; }
        .button-waves .wave:nth-child(5) { height: 10px; animation-delay: 0.4s; }

        @keyframes buttonWave {
            0%, 100% { transform: scaleY(0.3); }
            50% { transform: scaleY(1); }
        }

        .play-button.playing .button-waves .wave {
            animation-duration: 0.8s;
        }

        /* VS Divider */
        .vs-divider {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .vs-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--accent-copper), var(--accent-gold));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-white);
            position: relative;
            z-index: 3;
            box-shadow: 0 10px 30px rgba(212, 165, 116, 0.4);
        }

        .pulse-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            border: 2px solid var(--accent-copper);
            border-radius: 50%;
            animation: pulseRing 2s ease-out infinite;
        }

        .pulse-ring.delay-1 {
            animation-delay: 0.5s;
        }

        .pulse-ring.delay-2 {
            animation-delay: 1s;
        }

        @keyframes pulseRing {
            0% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(2);
                opacity: 0;
            }
        }

        /* Now Playing Display */
        .now-playing {
            background: linear-gradient(135deg, var(--accent-copper), var(--accent-gold));
            border-radius: 20px;
            padding: 1.5rem 2rem;
            margin: 3rem 0;
            box-shadow: 0 20px 60px rgba(212, 165, 116, 0.3);
            animation: slideUp 0.5s ease-out;
        }

        @keyframes slideUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .now-playing-content {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .now-playing-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: var(--primary-white);
            animation: spin 3s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .now-playing-info {
            flex: 1;
        }

        .track-title {
            color: var(--primary-white);
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.3rem;
        }

        .track-system {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .now-playing-visualizer {
            display: flex;
            gap: 3px;
            align-items: end;
            margin-right: 1rem;
        }

        .visualizer-bar {
            width: 4px;
            background: var(--primary-white);
            border-radius: 2px;
            animation: visualizer 1s ease-in-out infinite;
        }

        .visualizer-bar:nth-child(1) { height: 20px; animation-delay: 0s; }
        .visualizer-bar:nth-child(2) { height: 30px; animation-delay: 0.1s; }
        .visualizer-bar:nth-child(3) { height: 25px; animation-delay: 0.2s; }
        .visualizer-bar:nth-child(4) { height: 35px; animation-delay: 0.3s; }
        .visualizer-bar:nth-child(5) { height: 15px; animation-delay: 0.4s; }

        @keyframes visualizer {
            0%, 100% { transform: scaleY(0.3); }
            50% { transform: scaleY(1); }
        }

        .stop-button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-white);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .stop-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* Comparison Results */
        .comparison-results {
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 3rem 2rem;
            margin-top: 3rem;
        }

        .results-content h3 {
            color: var(--primary-white);
            font-family: var(--font-display);
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }

        .results-content p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            margin-bottom: 2.5rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .results-actions {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }

        .replay-button {
            background: transparent;
            border: 2px solid var(--accent-copper);
            border-radius: 50px;
            padding: 1rem 2rem;
            color: var(--accent-copper);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .replay-button:hover {
            background: var(--accent-copper);
            color: var(--primary-white);
            transform: translateY(-2px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .vs-divider {
                order: -1;
                margin: 1rem 0;
            }

            .vs-circle {
                width: 60px;
                height: 60px;
                font-size: 1.2rem;
            }

            .audio-card {
                padding: 2rem 1.5rem;
            }

            .results-actions {
                flex-direction: column;
            }

            .now-playing-content {
                flex-wrap: wrap;
                gap: 1rem;
            }
        }
    </style>
</head>

<body>
    <!-- Modern Navigation -->
    <nav class="navbar navbar-expand-lg luxury-navbar fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../index.html">
                <img src="../assets/logo-full-transparent.png" alt="Audio Excellence Logo" style="height: 2.5rem; width: auto; max-width: 220px; object-fit: contain; display: inline-block; vertical-align: middle;" />
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./music-systems.html">Music Systems</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./home-theatre.html">Home Theatre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./home-automation.html">Home Automation</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link active" href="./showcase.html">Showcase</a>
                    </li>
                </ul>
                <a href="./contact.html" class="cta-button ms-3">Book a Demo</a>
            </div>
        </div>
    </nav>

    <!-- Hero Showcase Section -->
    <section class="hero-showcase">
        <div class="container">
            <div class="hero-content text-center">
                <div class="exclusive-badge" data-aos="fade-up">EXCLUSIVE AUDIOPHILE EXPERIENCE</div>
                <h1 style="font-family: var(--font-display); font-size: clamp(3rem, 6vw, 5rem); font-weight: 300; margin-bottom: 2rem;" data-aos="fade-up" data-aos-delay="200">
                    The <span style="color: var(--accent-copper); font-weight: 600;">Ultimate</span> Audio Journey
                </h1>
                <p style="font-size: 1.5rem; margin-bottom: 3rem; max-width: 800px; margin-left: auto; margin-right: auto; opacity: 0.9;" data-aos="fade-up" data-aos-delay="400">
                    Where passion meets perfection. Experience music the way artists intended, with reference-grade systems that reveal every nuance, every emotion, every breath.
                </p>
                <a href="./contact.html" class="cta-button" style="font-size: 1.3rem; padding: 1.2rem 3rem;" data-aos="fade-up" data-aos-delay="600">Experience Excellence</a>
            </div>
        </div>
    </section>

    <!-- Revolutionary A/B Audio Testing -->
    <section class="audio-testing-section">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up">
                <div class="testing-badge">REVOLUTIONARY AUDIO COMPARISON</div>
                <h2 class="section-title"> 
                    <span style="color: #fff;">Hear the </span><span class="accent">Difference</span><span style="color: #fff;"> Yourself</span>
                </h2>
                <p class="lead" style="max-width: 800px; margin: 0 auto; color: var(--medium-gray);">
                    Experience the shocking difference between ordinary audio and our reference-grade systems.
                    Put on your headphones and prepare to be amazed.
                </p>
            </div>

            <div class="audio-comparison-container" data-aos="fade-up" data-aos-delay="200">
                <div class="comparison-header">
                    <div class="audio-warning">
                        <i class="fas fa-headphones"></i>
                        <span>For the best experience, please use headphones or quality speakers</span>
                    </div>
                </div>

                <div class="comparison-grid">
                    <!-- Regular System -->
                    <div class="audio-option regular-system" id="regularSystem">
                        <div class="audio-card">
                            <div class="audio-icon-container">
                                <div class="audio-icon regular">
                                    <i class="fas fa-volume-down"></i>
                                </div>
                                <div class="audio-waves regular">
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                </div>
                            </div>

                            <h3>Regular Audio Systems</h3>
                            <p class="audio-description">Standard consumer audio - compressed, limited, ordinary</p>

                            <div class="audio-specs">
                                <div class="spec-item">
                                    <span class="spec-label">Quality:</span>
                                    <span class="spec-value poor">MP3 Compressed</span>
                                </div>
                                <div class="spec-item">
                                    <span class="spec-label">Frequency:</span>
                                    <span class="spec-value poor">20Hz - 16kHz</span>
                                </div>
                                <div class="spec-item">
                                    <span class="spec-label">Dynamic Range:</span>
                                    <span class="spec-value poor">Limited</span>
                                </div>
                            </div>

                            <button class="play-button regular" id="playRegular">
                                <div class="play-icon">
                                    <i class="fas fa-play"></i>
                                </div>
                                <span class="play-text">Play Regular Audio</span>
                                <div class="button-waves">
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- VS Divider -->
                    <div class="vs-divider">
                        <div class="vs-circle">
                            <span>VS</span>
                            <div class="pulse-ring"></div>
                            <div class="pulse-ring delay-1"></div>
                            <div class="pulse-ring delay-2"></div>
                        </div>
                    </div>

                    <!-- High-End System -->
                    <div class="audio-option high-end-system" id="highEndSystem">
                        <div class="audio-card premium">
                            <div class="premium-badge">REFERENCE GRADE</div>
                            <div class="audio-icon-container">
                                <div class="audio-icon premium">
                                    <i class="fas fa-gem"></i>
                                </div>
                                <div class="audio-waves premium">
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                </div>
                            </div>

                            <h3>Audio Excellence Systems</h3>
                            <p class="audio-description">Reference-grade audiophile perfection - every detail revealed</p>

                            <div class="audio-specs">
                                <div class="spec-item">
                                    <span class="spec-label">Quality:</span>
                                    <span class="spec-value premium">Hi-Res Lossless</span>
                                </div>
                                <div class="spec-item">
                                    <span class="spec-label">Frequency:</span>
                                    <span class="spec-value premium">5Hz - 40kHz</span>
                                </div>
                                <div class="spec-item">
                                    <span class="spec-label">Dynamic Range:</span>
                                    <span class="spec-value premium">Unlimited</span>
                                </div>
                            </div>

                            <button class="play-button premium" id="playPremium">
                                <div class="play-icon">
                                    <i class="fas fa-play"></i>
                                </div>
                                <span class="play-text">Experience Excellence</span>
                                <div class="button-waves">
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Audio Players (Hidden) -->
                <audio id="regularAudio" preload="auto">
                    <source src="../audio/low_res.mp3" type="audio/mpeg">
                    Your browser does not support the audio element.
                </audio>

                <audio id="premiumAudio" preload="auto">
                    <source src="../audio/high_res.mp3" type="audio/mpeg">
                    Your browser does not support the audio element.
                </audio>

                <!-- Now Playing Display -->
                <div class="now-playing" id="nowPlaying" style="display: none;">
                    <div class="now-playing-content">
                        <div class="now-playing-icon">
                            <i class="fas fa-music"></i>
                        </div>
                        <div class="now-playing-info">
                            <div class="track-title" id="trackTitle">Audio Sample</div>
                            <div class="track-system" id="trackSystem">Regular System</div>
                        </div>
                        <div class="now-playing-visualizer">
                            <div class="visualizer-bar"></div>
                            <div class="visualizer-bar"></div>
                            <div class="visualizer-bar"></div>
                            <div class="visualizer-bar"></div>
                            <div class="visualizer-bar"></div>
                        </div>
                        <button class="stop-button" id="stopAudio">
                            <i class="fas fa-stop"></i>
                        </button>
                    </div>
                </div>

                <!-- Comparison Results -->
                <div class="comparison-results" id="comparisonResults" style="display: none;">
                    <div class="results-content" data-aos="fade-up">
                        <h3>Can You Hear the Difference?</h3>
                        <p>Most people are shocked by the dramatic improvement in clarity, depth, and emotional impact. This is just a small taste of what our reference-grade systems can deliver in your space.</p>
                        <div class="results-actions">
                            <a href="./contact.html" class="cta-button">Book Your Private Demo</a>
                            <button class="replay-button" id="replayComparison">
                                <i class="fas fa-redo"></i> Compare Again
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- The Audiophile Difference -->
    <section class="luxury-section">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">The <span class="accent">Audiophile</span> Difference</h2>
            <div class="row g-5">
                <div class="col-lg-6" data-aos="fade-right">
                    <h3 style="font-family: var(--font-display); font-size: 2.5rem; color: var(--deep-charcoal); margin-bottom: 2rem;">Why Settle for Ordinary?</h3>
                    <p style="font-size: 1.2rem; color: var(--medium-gray); margin-bottom: 2rem; line-height: 1.7;">
                        Most people have never experienced true high-fidelity audio. They've accepted compressed, lifeless sound as "good enough." But you're different. You understand that music is more than background noise—it's an emotional journey, a spiritual experience.
                    </p>
                    <p style="font-size: 1.2rem; color: var(--medium-gray); margin-bottom: 2rem; line-height: 1.7;">
                        Our reference-grade systems don't just play music; they transport you into the recording studio, the concert hall, the artist's soul. Every breath, every string vibration, every subtle harmonic becomes crystal clear.
                    </p>

                </div>
                <div class="col-lg-6" data-aos="fade-left">
                    <div class="luxury-card">
                        <h4 style="color: var(--accent-copper); margin-bottom: 1.5rem; font-family: var(--font-display);">What You'll Discover</h4>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin-bottom: 1rem; font-size: 1.1rem;"><i class="fas fa-check-circle" style="color: var(--accent-copper); margin-right: 1rem;"></i>Soundstage so wide you'll forget you're listening to speakers</li>
                            <li style="margin-bottom: 1rem; font-size: 1.1rem;"><i class="fas fa-check-circle" style="color: var(--accent-copper); margin-right: 1rem;"></i>Bass so tight and controlled it feels like live performance</li>
                            <li style="margin-bottom: 1rem; font-size: 1.1rem;"><i class="fas fa-check-circle" style="color: var(--accent-copper); margin-right: 1rem;"></i>Highs so pure they'll give you goosebumps</li>
                            <li style="margin-bottom: 1rem; font-size: 1.1rem;"><i class="fas fa-check-circle" style="color: var(--accent-copper); margin-right: 1rem;"></i>Midrange so natural you'll hear new details in familiar songs</li>
                            <li style="margin-bottom: 1rem; font-size: 1.1rem;"><i class="fas fa-check-circle" style="color: var(--accent-copper); margin-right: 1rem;"></i>Dynamics that make you feel the music's emotional peaks</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Social Proof & Statistics -->
    <section class="luxury-section">
        <div class="container text-center">
            <h2 class="section-title" data-aos="fade-up">Trusted by <span class="accent">Connoisseurs</span></h2>
            <div class="row g-4 mb-5">
                <div class="col-md-3" data-aos="fade-up" data-aos-delay="100">
                    <div class="stat-number">30+</div>
                    <p style="font-size: 1.1rem; color: var(--medium-gray);">Years of Excellence</p>
                </div>
                <div class="col-md-3" data-aos="fade-up" data-aos-delay="200">
                    <div class="stat-number">500+</div>
                    <p style="font-size: 1.1rem; color: var(--medium-gray);">Luxury Installations</p>
                </div>
                <div class="col-md-3" data-aos="fade-up" data-aos-delay="300">
                    <div class="stat-number">98%</div>
                    <p style="font-size: 1.1rem; color: var(--medium-gray);">Client Satisfaction</p>
                </div>
                <div class="col-md-3" data-aos="fade-up" data-aos-delay="400">
                    <div class="stat-number">21</div>
                    <p style="font-size: 1.1rem; color: var(--medium-gray);">Premium Brands</p>
                </div>
            </div>
            
            <div class="testimonial-card" data-aos="fade-up">
                <p style="font-size: 1.3rem; font-style: italic; margin-bottom: 1.5rem; color: var(--deep-charcoal);">
                    "I thought I knew what good sound was until I experienced Audio Excellence's McIntosh system. It's not just an upgrade—it's a revelation. Every album in my collection sounds like I'm hearing it for the first time."
                </p>
                <p style="font-weight: 600; color: var(--accent-copper);">— Dr. Michael Chen, Audiophile & Collector</p>
            </div>
        </div>
    </section>

    <!-- Psychological Triggers -->
    <section class="luxury-section" style="background: var(--deep-charcoal); color: var(--primary-white);">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6" data-aos="fade-right">
                    <h2 style="font-family: var(--font-display); font-size: 3rem; margin-bottom: 2rem; color: var(--primary-white);">
                        You Deserve <span style="color: var(--accent-copper);">Extraordinary</span>
                    </h2>
                    <p style="font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9; line-height: 1.7;">
                        Life is too short for mediocre sound. You've worked hard to afford the finer things. Your home reflects your success, your car embodies your taste, your watch speaks to your appreciation of craftsmanship.
                    </p>
                    <p style="font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9; line-height: 1.7;">
                        Why compromise on the one thing that touches your soul every single day? Your music deserves the same level of excellence you demand in every other aspect of your life.
                    </p>
                    <div style="background: rgba(212, 165, 116, 0.1); padding: 2rem; border-radius: var(--border-radius); border-left: 4px solid var(--accent-copper);">
                        <p style="font-size: 1.1rem; margin: 0; font-weight: 500;">
                            <i class="fas fa-quote-left" style="color: var(--accent-copper); margin-right: 1rem;"></i>
                            "The difference between good and extraordinary is often just one decision. Make yours count."
                        </p>
                    </div>
                </div>
                <div class="col-lg-6" data-aos="fade-left">
                    <div style="background: var(--primary-white); padding: 3rem; border-radius: var(--border-radius); color: var(--deep-charcoal);">
                        <h3 style="color: var(--accent-copper); margin-bottom: 2rem; font-family: var(--font-display);">Exclusive Benefits</h3>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin-bottom: 1.5rem; font-size: 1.1rem;"><i class="fas fa-crown" style="color: var(--accent-copper); margin-right: 1rem;"></i>Private listening sessions in our reference room</li>
                            <li style="margin-bottom: 1.5rem; font-size: 1.1rem;"><i class="fas fa-user-tie" style="color: var(--accent-copper); margin-right: 1rem;"></i>Personal audio consultant assigned to your project</li>
                            <li style="margin-bottom: 1.5rem; font-size: 1.1rem;"><i class="fas fa-tools" style="color: var(--accent-copper); margin-right: 1rem;"></i>White-glove installation and calibration</li>
                            <li style="margin-bottom: 1.5rem; font-size: 1.1rem;"><i class="fas fa-shield-alt" style="color: var(--accent-copper); margin-right: 1rem;"></i>Lifetime support and system optimization</li>
                            <li style="margin-bottom: 1.5rem; font-size: 1.1rem;"><i class="fas fa-star" style="color: var(--accent-copper); margin-right: 1rem;"></i>Access to limited-edition and pre-release equipment</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA -->
    <section class="luxury-section" style="background: linear-gradient(135deg, var(--luxury-cream), var(--warm-white)); text-align: center;">
        <div class="container">
            <div data-aos="zoom-in" style="max-width: 800px; margin: 0 auto; padding: 4rem 3rem; background: var(--primary-white); border-radius: var(--border-radius); border: 1px solid var(--accent-copper); box-shadow: var(--shadow-deep);">
                <h2 style="font-family: var(--font-display); font-size: 3rem; color: var(--deep-charcoal); margin-bottom: 1.5rem; font-weight: 300;">
                    Your Journey to <span style="color: var(--accent-copper); font-weight: 600;">Audio Nirvana</span> Starts Here
                </h2>
                <p style="font-size: 1.4rem; color: var(--medium-gray); margin-bottom: 2.5rem; line-height: 1.6;">
                    Don't spend another day settling for ordinary sound. Experience what true audiophile quality feels like.
                </p>
                <a href="./contact.html" class="cta-button" style="font-size: 1.3rem; padding: 1.2rem 3.5rem; margin-right: 1rem;">Book Private Demo</a>
                <p style="font-size: 0.95rem; color: var(--medium-gray); margin-top: 1.5rem; font-style: italic;">
                    Limited availability • By appointment only • Serious inquiries welcome
                </p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="luxury-footer">
        <div class="container">
            <div style="max-width: 800px; margin: 0 auto;">
                <h3 style="font-family: var(--font-display); font-size: 2rem; color: var(--accent-copper); margin-bottom: 1.5rem; font-weight: 600;">Audio Excellence</h3>
                <p style="color: var(--light-gray); margin-bottom: 2rem; font-size: 1.1rem; line-height: 1.6;">
                    Gateway Office Park, 1 Sugar Close, Umhlanga Ridge, 4320<br>
                    <strong>(*************</strong> | 
                    <a href="mailto:<EMAIL>" style="color: var(--accent-copper); text-decoration: none;"><EMAIL></a>
                </p>
                
                <div style="margin-bottom: 2rem;">
                    <a href="https://www.facebook.com/audioexcellencesa/" target="_blank" rel="noopener" aria-label="Facebook" style="color: var(--accent-copper); font-size: 1.75rem; margin: 0 1.5rem; transition: var(--transition);">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://www.instagram.com/excellenceaudio/" target="_blank" rel="noopener" aria-label="Instagram" style="color: var(--accent-copper); font-size: 1.75rem; margin: 0 1.5rem; transition: var(--transition);">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>
                
                <p style="color: var(--light-gray); margin-top: 2rem; font-size: 0.95rem;">
                    © 2025 Audio Excellence Pty Ltd. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.luxury-navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // A/B Audio Testing System
        class AudioComparison {
            constructor() {
                this.regularAudio = document.getElementById('regularAudio');
                this.premiumAudio = document.getElementById('premiumAudio');
                this.playRegularBtn = document.getElementById('playRegular');
                this.playPremiumBtn = document.getElementById('playPremium');
                this.nowPlaying = document.getElementById('nowPlaying');
                this.trackTitle = document.getElementById('trackTitle');
                this.trackSystem = document.getElementById('trackSystem');
                this.stopBtn = document.getElementById('stopAudio');
                this.comparisonResults = document.getElementById('comparisonResults');
                this.replayBtn = document.getElementById('replayComparison');

                this.currentAudio = null;
                this.hasPlayedBoth = false;
                this.playedRegular = false;
                this.playedPremium = false;

                this.init();
            }

            init() {
                this.bindEvents();
                this.setupAudioEvents();
            }

            bindEvents() {
                this.playRegularBtn.addEventListener('click', () => this.playRegular());
                this.playPremiumBtn.addEventListener('click', () => this.playPremium());
                this.stopBtn.addEventListener('click', () => this.stopAudio());
                this.replayBtn.addEventListener('click', () => this.resetComparison());
            }

            setupAudioEvents() {
                // Regular audio events
                this.regularAudio.addEventListener('loadstart', () => this.showLoading('regular'));
                this.regularAudio.addEventListener('canplay', () => this.hideLoading('regular'));
                this.regularAudio.addEventListener('ended', () => this.onAudioEnded('regular'));

                // Premium audio events
                this.premiumAudio.addEventListener('loadstart', () => this.showLoading('premium'));
                this.premiumAudio.addEventListener('canplay', () => this.hideLoading('premium'));
                this.premiumAudio.addEventListener('ended', () => this.onAudioEnded('premium'));
            }

            playRegular() {
                this.stopCurrentAudio();
                this.currentAudio = this.regularAudio;
                this.playedRegular = true;

                // Update UI
                this.playRegularBtn.classList.add('playing');
                this.playRegularBtn.innerHTML = `
                    <div class="play-icon"><i class="fas fa-pause"></i></div>
                    <span class="play-text">Playing Regular Audio</span>
                    <div class="button-waves">
                        <div class="wave"></div>
                        <div class="wave"></div>
                        <div class="wave"></div>
                    </div>
                `;

                // Show now playing
                this.showNowPlaying('Audio Sample', 'Regular Consumer System');

                // Play audio
                this.regularAudio.play().catch(e => {
                    console.error('Error playing regular audio:', e);
                    this.showError('Unable to play audio. Please check your connection.');
                });

                // Add visual effects
                this.addPlayingEffects('regular');
            }

            playPremium() {
                this.stopCurrentAudio();
                this.currentAudio = this.premiumAudio;
                this.playedPremium = true;

                // Update UI
                this.playPremiumBtn.classList.add('playing');
                this.playPremiumBtn.innerHTML = `
                    <div class="play-icon"><i class="fas fa-pause"></i></div>
                    <span class="play-text">Experiencing Excellence</span>
                    <div class="button-waves">
                        <div class="wave"></div>
                        <div class="wave"></div>
                        <div class="wave"></div>
                        <div class="wave"></div>
                        <div class="wave"></div>
                    </div>
                `;

                // Show now playing
                this.showNowPlaying('Audio Sample', 'Audio Excellence Reference System');

                // Play audio
                this.premiumAudio.play().catch(e => {
                    console.error('Error playing premium audio:', e);
                    this.showError('Unable to play audio. Please check your connection.');
                });

                // Add visual effects
                this.addPlayingEffects('premium');
            }

            stopAudio() {
                if (this.currentAudio) {
                    this.currentAudio.pause();
                    this.currentAudio.currentTime = 0;
                    this.currentAudio = null;
                }

                this.resetButtons();
                this.hideNowPlaying();
                this.removePlayingEffects();
            }

            stopCurrentAudio() {
                if (this.currentAudio) {
                    this.currentAudio.pause();
                    this.currentAudio.currentTime = 0;
                }
                this.resetButtons();
                this.removePlayingEffects();
            }

            resetButtons() {
                // Reset regular button
                this.playRegularBtn.classList.remove('playing');
                this.playRegularBtn.innerHTML = `
                    <div class="play-icon"><i class="fas fa-play"></i></div>
                    <span class="play-text">Play Regular Audio</span>
                    <div class="button-waves">
                        <div class="wave"></div>
                        <div class="wave"></div>
                        <div class="wave"></div>
                    </div>
                `;

                // Reset premium button
                this.playPremiumBtn.classList.remove('playing');
                this.playPremiumBtn.innerHTML = `
                    <div class="play-icon"><i class="fas fa-play"></i></div>
                    <span class="play-text">Experience Excellence</span>
                    <div class="button-waves">
                        <div class="wave"></div>
                        <div class="wave"></div>
                        <div class="wave"></div>
                        <div class="wave"></div>
                        <div class="wave"></div>
                    </div>
                `;
            }

            showNowPlaying(title, system) {
                this.trackTitle.textContent = title;
                this.trackSystem.textContent = system;
                this.nowPlaying.style.display = 'block';
            }

            hideNowPlaying() {
                this.nowPlaying.style.display = 'none';
            }

            addPlayingEffects(type) {
                const option = document.getElementById(type === 'regular' ? 'regularSystem' : 'highEndSystem');
                option.classList.add('playing');

                // Enhance audio waves animation
                const waves = option.querySelectorAll('.audio-waves .wave');
                waves.forEach(wave => {
                    wave.style.animationDuration = '0.8s';
                });
            }

            removePlayingEffects() {
                document.querySelectorAll('.audio-option').forEach(option => {
                    option.classList.remove('playing');
                    const waves = option.querySelectorAll('.audio-waves .wave');
                    waves.forEach(wave => {
                        wave.style.animationDuration = '1.5s';
                    });
                });
            }

            onAudioEnded(type) {
                this.stopAudio();
                this.checkCompletion();
            }

            checkCompletion() {
                if (this.playedRegular && this.playedPremium) {
                    this.showComparisonResults();
                }
            }

            showComparisonResults() {
                this.comparisonResults.style.display = 'block';
                this.comparisonResults.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Add celebration effect
                this.celebrateCompletion();
            }

            celebrateCompletion() {
                // Create floating particles effect
                const container = this.comparisonResults;
                for (let i = 0; i < 20; i++) {
                    setTimeout(() => {
                        this.createParticle(container);
                    }, i * 100);
                }
            }

            createParticle(container) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: absolute;
                    width: 6px;
                    height: 6px;
                    background: var(--accent-copper);
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 1000;
                `;

                const rect = container.getBoundingClientRect();
                particle.style.left = Math.random() * rect.width + 'px';
                particle.style.top = rect.height + 'px';

                container.appendChild(particle);

                // Animate particle
                particle.animate([
                    { transform: 'translateY(0px) scale(1)', opacity: 1 },
                    { transform: 'translateY(-100px) scale(0)', opacity: 0 }
                ], {
                    duration: 2000,
                    easing: 'ease-out'
                }).onfinish = () => particle.remove();
            }

            resetComparison() {
                this.stopAudio();
                this.playedRegular = false;
                this.playedPremium = false;
                this.hasPlayedBoth = false;
                this.comparisonResults.style.display = 'none';

                // Scroll back to comparison
                document.querySelector('.audio-comparison-container').scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }

            showLoading(type) {
                const button = type === 'regular' ? this.playRegularBtn : this.playPremiumBtn;
                const icon = button.querySelector('.play-icon i');
                icon.className = 'fas fa-spinner fa-spin';
            }

            hideLoading(type) {
                const button = type === 'regular' ? this.playRegularBtn : this.playPremiumBtn;
                const icon = button.querySelector('.play-icon i');
                icon.className = 'fas fa-play';
            }

            showError(message) {
                // Create error notification
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #ff6b6b;
                    color: white;
                    padding: 1rem 2rem;
                    border-radius: 10px;
                    z-index: 10000;
                    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
                `;
                errorDiv.textContent = message;
                document.body.appendChild(errorDiv);

                // Remove after 5 seconds
                setTimeout(() => {
                    errorDiv.remove();
                }, 5000);
            }
        }

        // Initialize Audio Comparison when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            new AudioComparison();

            // Add hover effects to audio cards
            document.querySelectorAll('.audio-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    const waves = this.querySelectorAll('.audio-waves .wave');
                    waves.forEach(wave => {
                        wave.style.animationDuration = '1s';
                    });
                });

                card.addEventListener('mouseleave', function() {
                    if (!this.closest('.audio-option').classList.contains('playing')) {
                        const waves = this.querySelectorAll('.audio-waves .wave');
                        waves.forEach(wave => {
                            wave.style.animationDuration = '1.5s';
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>

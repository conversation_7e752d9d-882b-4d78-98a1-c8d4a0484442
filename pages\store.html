<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Luxury Audio Store – Audio Excellence</title>
    <meta name="description" content="Discover our curated collection of premium audio equipment, home theatre systems, and luxury automation solutions. Experience excellence in every detail.">
    <link rel="icon" type="image/png" href="../assets/logo-full-transparent.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700;800;900&family=Helvetica+Neue:wght@300;400;500;600;700&family=Avenir:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            /* Luxury Color Palette */
            --primary-black: #000000;
            --primary-white: #ffffff;
            --luxury-cream: #f8f6f0;
            --warm-white: #fafafa;
            --accent-copper: #d4a574;
            --accent-gold: #c9a96e;
            --deep-charcoal: #1d1d1f;
            --medium-gray: #86868b;
            --light-gray: #f5f5f7;
            --glass-white: rgba(255, 255, 255, 0.8);
            --glass-black: rgba(0, 0, 0, 0.8);
            
            /* Effects */
            --shadow-luxury: 0 8px 32px rgba(0, 0, 0, 0.12);
            --shadow-copper: 0 4px 20px rgba(212, 165, 116, 0.3);
            --shadow-deep: 0 16px 64px rgba(0, 0, 0, 0.15);
            --shadow-product: 0 12px 40px rgba(0, 0, 0, 0.08);
            --border-radius: 12px;
            --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            
            /* Typography */
            --font-primary: 'SF Pro Display', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-display: 'Playfair Display', serif;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-primary);
            background: var(--warm-white);
            color: var(--deep-charcoal);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* Navigation */
        .luxury-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: var(--glass-white);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: var(--transition);
            padding: 0.5rem 0;
        }

        .luxury-navbar.scrolled {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-luxury);
        }

        .navbar-brand {
            font-family: var(--font-display);
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--deep-charcoal) !important;
            text-decoration: none;
            letter-spacing: -0.02em;
        }

        .navbar-nav .nav-link {
            color: var(--deep-charcoal) !important;
            font-weight: 500;
            font-size: 1rem;
            padding: 0.75rem 1.5rem !important;
            border-radius: var(--border-radius);
            transition: var(--transition);
            position: relative;
            margin: 0 0.25rem;
        }

        .navbar-nav .nav-link:hover {
            color: var(--accent-copper) !important;
            background: rgba(212, 165, 116, 0.1);
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            color: var(--accent-copper) !important;
            background: rgba(212, 165, 116, 0.15);
            font-weight: 600;
        }

        .cta-button {
            background: linear-gradient(135deg, var(--accent-copper), var(--accent-gold));
            color: var(--primary-white) !important;
            padding: 0.75rem 2rem !important;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            box-shadow: var(--shadow-copper);
            border: none;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-deep);
            color: var(--primary-white) !important;
            background: linear-gradient(135deg, var(--accent-gold), var(--accent-copper));
        }

        /* Store Sections */
        .store-section {
            padding: 6rem 0;
            position: relative;
        }

        .store-section:nth-child(even) {
            background: var(--warm-white);
        }

        .store-section:nth-child(odd) {
            background: var(--luxury-cream);
        }

        .section-title {
            font-family: var(--font-display);
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 300;
            text-align: center;
            margin-bottom: 4rem;
            color: var(--deep-charcoal);
            letter-spacing: -0.02em;
        }

        .section-title .accent {
            color: var(--accent-copper);
            font-weight: 600;
        }

        /* Hero Section */
        .store-hero {
            height: 70vh;
            background: linear-gradient(135deg, rgba(0,0,0,0.6), rgba(29,29,31,0.8)), url('../assets/BG-wood.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin-top: 80px;
        }

        .hero-content {
            max-width: 800px;
            padding: 0 2rem;
            color: var(--primary-white);
        }

        .hero-title {
            font-family: var(--font-display);
            font-size: clamp(3rem, 6vw, 5rem);
            font-weight: 300;
            line-height: 1.1;
            margin-bottom: 2rem;
            letter-spacing: -0.02em;
        }

        .hero-subtitle {
            font-size: clamp(1.2rem, 2.5vw, 1.6rem);
            margin-bottom: 3rem;
            font-weight: 400;
            line-height: 1.5;
            opacity: 0.9;
        }

        /* Category Filter */
        .category-filter {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 4rem;
        }

        .filter-btn {
            background: var(--primary-white);
            border: 2px solid var(--accent-copper);
            color: var(--accent-copper);
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: var(--transition);
            cursor: pointer;
            font-size: 1rem;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: var(--accent-copper);
            color: var(--primary-white);
            transform: translateY(-2px);
            box-shadow: var(--shadow-copper);
        }

        /* Product Grid */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2.5rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .product-card {
            background: var(--primary-white);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-product);
            transition: var(--transition);
            position: relative;
            border: 1px solid rgba(212, 165, 116, 0.1);
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .product-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-deep);
            border-color: var(--accent-copper);
        }

        .product-image {
            position: relative;
            height: 280px;
            overflow: hidden;
            background: var(--light-gray);
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .product-card:hover .product-image img {
            transform: scale(1.05);
        }

        .product-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: var(--accent-copper);
            color: var(--primary-white);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .product-info {
            padding: 2rem;
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .product-brand {
            color: var(--accent-copper);
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
        }

        .product-title {
            font-family: var(--font-display);
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--deep-charcoal);
            margin-bottom: 1rem;
            line-height: 1.3;
        }

        .product-description {
            color: var(--medium-gray);
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .product-price {
            font-family: var(--font-display);
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--deep-charcoal);
            margin-bottom: 1.5rem;
        }

        .product-price .currency {
            font-size: 1.2rem;
            color: var(--medium-gray);
        }

        .product-actions {
            display: flex;
            gap: 0.75rem;
            align-items: center;
            margin-top: auto;
            padding-top: 1rem;
        }

        .btn-primary-store {
            background: linear-gradient(135deg, var(--accent-copper), var(--accent-gold));
            color: var(--primary-white);
            border: none;
            padding: 0.6rem 1.2rem;
            border-radius: 8px;
            font-weight: 600;
            transition: var(--transition);
            font-size: 0.9rem;
            flex: 1;
            min-height: 42px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-primary-store:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-copper);
            color: var(--primary-white);
        }

        .btn-secondary-store {
            background: transparent;
            color: var(--accent-copper);
            border: 2px solid var(--accent-copper);
            padding: 0.6rem 1.2rem;
            border-radius: 8px;
            font-weight: 600;
            transition: var(--transition);
            text-decoration: none;
            font-size: 0.9rem;
            flex: 1;
            min-height: 42px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-secondary-store:hover {
            background: var(--accent-copper);
            color: var(--primary-white);
            transform: translateY(-1px);
        }

        .wishlist-btn {
            background: none;
            border: none;
            color: var(--medium-gray);
            font-size: 1.5rem;
            padding: 0.5rem;
            border-radius: 50%;
            transition: var(--transition);
            cursor: pointer;
        }

        .wishlist-btn:hover,
        .wishlist-btn.active {
            color: #e74c3c;
            transform: scale(1.1);
        }

        /* Featured Section */
        .featured-section {
            background: var(--primary-black);
            color: var(--primary-white);
        }

        .featured-section .section-title {
            color: var(--primary-white);
        }

        /* Product Modal */
        .product-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            backdrop-filter: blur(10px);
        }

        .product-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-content {
            background: var(--primary-white);
            border-radius: var(--border-radius);
            max-width: 900px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            box-shadow: var(--shadow-deep);
            animation: slideUp 0.3s ease-out;
        }

        .modal-header {
            padding: 2rem 2rem 1rem;
            border-bottom: 1px solid var(--light-gray);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 2rem;
            color: var(--medium-gray);
            cursor: pointer;
            transition: var(--transition);
        }

        .modal-close:hover {
            color: var(--accent-copper);
            transform: scale(1.1);
        }

        .modal-body {
            padding: 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
        }

        .modal-image {
            width: 100%;
            height: 300px;
            object-fit: cover;
            border-radius: var(--border-radius);
        }

        .modal-specs {
            background: var(--light-gray);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin: 1.5rem 0;
        }

        .spec-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .spec-row:last-child {
            border-bottom: none;
        }

        .spec-label {
            font-weight: 600;
            color: var(--deep-charcoal);
        }

        .spec-value {
            color: var(--medium-gray);
        }

        /* Consultation Button Fix */
        .consultation-cta {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px;
        }

        .consultation-content {
            text-align: center;
            padding: 4rem 3rem;
        }

        .consultation-content h2 {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .product-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .category-filter {
                flex-direction: column;
                align-items: center;
            }

            .store-section {
                padding: 4rem 0;
            }

            .modal-body {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .consultation-content {
                padding: 2rem 1.5rem;
            }
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg luxury-navbar">
        <div class="container">
            <a class="navbar-brand" href="../index.html">
                <img src="../assets/logo-full-transparent.png" alt="Audio Excellence Logo" style="height: 2.5rem; width: auto; max-width: 220px; object-fit: contain; display: inline-block; vertical-align: middle;" />
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="./store.html">Store</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./music-systems.html">Music Systems</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./home-theatre.html">Home Theatre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./home-automation.html">Home Automation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./showcase.html">Showcase</a>
                    </li>
                </ul>
                <a href="./contact.html" class="cta-button ms-3">Book a Demo</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="store-hero">
        <div class="hero-content" data-aos="fade-up" data-aos-duration="1200">
            <h1 class="hero-title">Luxury Audio <span style="color: var(--accent-copper);">Collection</span></h1>
            <p class="hero-subtitle">Discover our curated selection of the world's finest audio equipment, meticulously chosen for discerning audiophiles and luxury enthusiasts.</p>
            <div class="mt-4">
                <a href="#products" class="cta-button" style="font-size: 1.1rem; padding: 1rem 2.5rem;">
                    <i class="fas fa-gem me-2"></i>Explore Collection
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section class="featured-section store-section" id="featured">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Featured <span class="accent">Excellence</span></h2>
            <div class="product-grid px-3">
                <!-- McIntosh MA352 -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="product-image">
                        <img src="../assets/build2.png" alt="McIntosh MA352 Integrated Amplifier" loading="lazy">
                        <div class="product-badge">Flagship</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">McIntosh</div>
                        <h3 class="product-title">MA352 Hybrid Integrated Amplifier</h3>
                        <p class="product-description">The perfect marriage of vacuum tube warmth and solid state control. 200 watts per channel of pure McIntosh magic.</p>
                        <div class="product-price">
                            <span class="currency">R</span> 189,900
                        </div>
                        <div class="product-actions">
                            <button class="btn-primary-store">
                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                            </button>
                            <a href="#" class="btn-secondary-store">Details</a>
                            <button class="wishlist-btn">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- KEF R11 Meta -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="product-image">
                        <img src="../assets/Home-White-Speakers-Gold-Rings-sml.jpg" alt="KEF R11 Meta Floorstanding Speakers" loading="lazy">
                        <div class="product-badge">New</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">KEF</div>
                        <h3 class="product-title">R11 Meta Floorstanding Speakers</h3>
                        <p class="product-description">Revolutionary Metamaterial Absorption Technology delivers the purest possible sound from the innovative Uni-Q driver array.</p>
                        <div class="product-price">
                            <span class="currency">R</span> 84,900
                        </div>
                        <div class="product-actions">
                            <button class="btn-primary-store">
                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                            </button>
                            <a href="#" class="btn-secondary-store">Details</a>
                            <button class="wishlist-btn">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Klipsch Heritage -->
                <div class="product-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="product-image">
                        <img src="../assets/build1.png" alt="Klipsch Heritage Forte IV" loading="lazy">
                        <div class="product-badge">Heritage</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">Klipsch</div>
                        <h3 class="product-title">Heritage Forte IV Speakers</h3>
                        <p class="product-description">Hand-crafted in Arkansas, these legendary speakers deliver the live music experience with unmatched dynamics and presence.</p>
                        <div class="product-price">
                            <span class="currency">R</span> 67,500
                        </div>
                        <div class="product-actions">
                            <button class="btn-primary-store">
                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                            </button>
                            <a href="#" class="btn-secondary-store">Details</a>
                            <button class="wishlist-btn">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Categories Section -->
    <section class="store-section" id="products">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Premium <span class="accent">Categories</span></h2>

            <!-- Category Filter -->
            <div class="category-filter" data-aos="fade-up" data-aos-delay="200">
                <button class="filter-btn active" data-category="all">All Products</button>
                <button class="filter-btn" data-category="amplifiers">Amplifiers</button>
                <button class="filter-btn" data-category="speakers">Speakers</button>
                <button class="filter-btn" data-category="turntables">Turntables</button>
                <button class="filter-btn" data-category="streaming">Streaming</button>
                <button class="filter-btn" data-category="cables">Cables</button>
                <button class="filter-btn" data-category="accessories">Accessories</button>
            </div>

            <!-- Products Grid -->
            <div class="product-grid px-3" id="productGrid">
                <!-- NAD M33 -->
                <div class="product-card" data-category="amplifiers streaming" data-aos="fade-up" data-aos-delay="100">
                    <div class="product-image">
                        <img src="../assets/audio-system.jpeg" alt="NAD M33 BluOS Streaming DAC Amplifier" loading="lazy">
                        <div class="product-badge">Masters Series</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">NAD</div>
                        <h3 class="product-title">M33 BluOS Streaming DAC Amplifier</h3>
                        <p class="product-description">Revolutionary Eigentakt amplification meets high-resolution streaming. The future of integrated amplification.</p>
                        <div class="product-price">
                            <span class="currency">R</span> 89,900
                        </div>
                        <div class="product-actions">
                            <button class="btn-primary-store">
                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                            </button>
                            <a href="#" class="btn-secondary-store">Details</a>
                            <button class="wishlist-btn">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Pro-Ject Debut Carbon EVO -->
                <div class="product-card" data-category="turntables" data-aos="fade-up" data-aos-delay="200">
                    <div class="product-image">
                        <div style="height: 100%; background: linear-gradient(135deg, #f5f5f7, #e8e8ea); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-record-vinyl" style="font-size: 4rem; color: var(--accent-copper);"></i>
                        </div>
                        <div class="product-badge">Audiophile</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">Pro-Ject</div>
                        <h3 class="product-title">Debut Carbon EVO Turntable</h3>
                        <p class="product-description">Precision-engineered turntable with carbon fiber tonearm and Ortofon 2M Red cartridge. Vinyl playback perfected.</p>
                        <div class="product-price">
                            <span class="currency">R</span> 12,900
                        </div>
                        <div class="product-actions">
                            <button class="btn-primary-store">
                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                            </button>
                            <a href="#" class="btn-secondary-store">Details</a>
                            <button class="wishlist-btn">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Bluesound Node -->
                <div class="product-card" data-category="streaming" data-aos="fade-up" data-aos-delay="300">
                    <div class="product-image">
                        <img src="../assets/Home-Pink-KEF-Muo-Wireless-Speaker-sml.jpg" alt="Bluesound Node Wireless Streamer" loading="lazy">
                        <div class="product-badge">Wireless</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">Bluesound</div>
                        <h3 class="product-title">Node Wireless Multi-Room Streamer</h3>
                        <p class="product-description">High-resolution wireless streaming with MQA support. Connect your entire home with pristine audio quality.</p>
                        <div class="product-price">
                            <span class="currency">R</span> 8,900
                        </div>
                        <div class="product-actions">
                            <button class="btn-primary-store">
                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                            </button>
                            <a href="#" class="btn-secondary-store">Details</a>
                            <button class="wishlist-btn">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Supra Cables -->
                <div class="product-card" data-category="cables" data-aos="fade-up" data-aos-delay="400">
                    <div class="product-image">
                        <div style="height: 100%; background: linear-gradient(135deg, #1a1a1a, #2d2d2d); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-plug" style="font-size: 4rem; color: var(--accent-copper);"></i>
                        </div>
                        <div class="product-badge">Premium</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">Supra Cables</div>
                        <h3 class="product-title">Quadrax Bi-Wire Speaker Cable</h3>
                        <p class="product-description">Swedish-engineered speaker cables with exceptional conductivity and shielding. Unleash your system's true potential.</p>
                        <div class="product-price">
                            <span class="currency">R</span> 2,890<span style="font-size: 1rem; color: var(--medium-gray);">/meter</span>
                        </div>
                        <div class="product-actions">
                            <button class="btn-primary-store">
                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                            </button>
                            <a href="#" class="btn-secondary-store">Details</a>
                            <button class="wishlist-btn">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- IsoTek V5 Polaris -->
                <div class="product-card" data-category="accessories" data-aos="fade-up" data-aos-delay="500">
                    <div class="product-image">
                        <div style="height: 100%; background: linear-gradient(135deg, #f8f6f0, #e8e6e0); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-bolt" style="font-size: 4rem; color: var(--accent-copper);"></i>
                        </div>
                        <div class="product-badge">Essential</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">IsoTek</div>
                        <h3 class="product-title">V5 Polaris Power Conditioner</h3>
                        <p class="product-description">Advanced power conditioning technology removes noise and interference, revealing hidden musical details.</p>
                        <div class="product-price">
                            <span class="currency">R</span> 18,900
                        </div>
                        <div class="product-actions">
                            <button class="btn-primary-store">
                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                            </button>
                            <a href="#" class="btn-secondary-store">Details</a>
                            <button class="wishlist-btn">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Wharfedale Linton Heritage -->
                <div class="product-card" data-category="speakers" data-aos="fade-up" data-aos-delay="600">
                    <div class="product-image">
                        <div style="height: 100%; background: linear-gradient(135deg, #8B4513, #A0522D); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-volume-up" style="font-size: 4rem; color: var(--primary-white);"></i>
                        </div>
                        <div class="product-badge">Heritage</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">Wharfedale</div>
                        <h3 class="product-title">Linton Heritage Speakers</h3>
                        <p class="product-description">Classic British sound with modern engineering. Warm, musical presentation that brings recordings to life.</p>
                        <div class="product-price">
                            <span class="currency">R</span> 24,900
                        </div>
                        <div class="product-actions">
                            <button class="btn-primary-store">
                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                            </button>
                            <a href="#" class="btn-secondary-store">Details</a>
                            <button class="wishlist-btn">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Rotel A14 MKII -->
                <div class="product-card" data-category="amplifiers" data-aos="fade-up" data-aos-delay="700">
                    <div class="product-image">
                        <div style="height: 100%; background: linear-gradient(135deg, #2c2c2c, #1a1a1a); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-sliders-h" style="font-size: 4rem; color: var(--accent-copper);"></i>
                        </div>
                        <div class="product-badge">Integrated</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">Rotel</div>
                        <h3 class="product-title">A14 MKII Integrated Amplifier</h3>
                        <p class="product-description">Exceptional value meets audiophile performance. Clean, powerful amplification with digital connectivity.</p>
                        <div class="product-price">
                            <span class="currency">R</span> 19,900
                        </div>
                        <div class="product-actions">
                            <button class="btn-primary-store">
                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                            </button>
                            <a href="#" class="btn-secondary-store">Details</a>
                            <button class="wishlist-btn">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Mission QX-2 MKII -->
                <div class="product-card" data-category="speakers" data-aos="fade-up" data-aos-delay="800">
                    <div class="product-image">
                        <div style="height: 100%; background: linear-gradient(135deg, #f0f0f0, #d0d0d0); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-music" style="font-size: 4rem; color: var(--accent-copper);"></i>
                        </div>
                        <div class="product-badge">Bookshelf</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">Mission</div>
                        <h3 class="product-title">QX-2 MKII Bookshelf Speakers</h3>
                        <p class="product-description">Compact speakers with big sound. Perfect for smaller spaces without compromising on audio quality.</p>
                        <div class="product-price">
                            <span class="currency">R</span> 8,900
                        </div>
                        <div class="product-actions">
                            <button class="btn-primary-store">
                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                            </button>
                            <a href="#" class="btn-secondary-store">Details</a>
                            <button class="wishlist-btn">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- SVS SB-3000 -->
                <div class="product-card" data-category="speakers accessories" data-aos="fade-up" data-aos-delay="900">
                    <div class="product-image">
                        <div style="height: 100%; background: linear-gradient(135deg, #000000, #1a1a1a); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-drum" style="font-size: 4rem; color: var(--accent-copper);"></i>
                        </div>
                        <div class="product-badge">Subwoofer</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">SVS</div>
                        <h3 class="product-title">SB-3000 Sealed Subwoofer</h3>
                        <p class="product-description">Compact sealed subwoofer with app control. Deep, accurate bass that integrates seamlessly with any system.</p>
                        <div class="product-price">
                            <span class="currency">R</span> 32,900
                        </div>
                        <div class="product-actions">
                            <button class="btn-primary-store">
                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                            </button>
                            <a href="#" class="btn-secondary-store">Details</a>
                            <button class="wishlist-btn">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Consultation CTA Section -->
    <section class="store-section consultation-cta" style="background: linear-gradient(135deg, var(--luxury-cream), var(--warm-white));">
        <div class="container">
            <div class="consultation-content" data-aos="zoom-in" style="max-width: 900px; margin: 0 auto; background: var(--primary-white); border-radius: var(--border-radius); border: 1px solid var(--accent-copper); box-shadow: var(--shadow-deep);">
                <h2 style="font-family: var(--font-display); font-size: 3rem; color: var(--deep-charcoal); font-weight: 300;">Need Expert <span style="color: var(--accent-copper); font-weight: 600;">Guidance?</span></h2>
                <p style="font-size: 1.4rem; color: var(--medium-gray); margin-bottom: 2.5rem; line-height: 1.6;">Our audio specialists are here to help you build the perfect system for your space and preferences. Book a personalized consultation today.</p>
                <div style="display: flex; gap: 1.5rem; justify-content: center; flex-wrap: wrap;">
                    <a href="./contact.html" class="cta-button" style="font-size: 1.2rem; padding: 1.2rem 3rem; display: flex; align-items: center;">
                        <i class="fas fa-calendar-alt me-2"></i>Book Consultation
                    </a>
                    <a href="./showcase.html" class="btn-secondary-store" style="font-size: 1.2rem; padding: 1.2rem 3rem; text-decoration: none;">
                        <i class="fas fa-eye me-2"></i>Visit Showroom
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: var(--deep-charcoal); padding: 4rem 0 3rem; text-align: center; border-top: 1px solid rgba(212, 165, 116, 0.2);">
        <div class="container">
            <div style="max-width: 800px; margin: 0 auto;">
                <h3 style="font-family: var(--font-display); font-size: 2rem; color: var(--accent-copper); margin-bottom: 1.5rem; font-weight: 600;">Audio Excellence</h3>
                <p style="color: var(--light-gray); margin-bottom: 2rem; font-size: 1.1rem; line-height: 1.6;">
                    Gateway Office Park, 1 Sugar Close, Umhlanga Ridge, 4320<br>
                    <strong>(*************</strong> |
                    <a href="mailto:<EMAIL>" style="color: var(--accent-copper); text-decoration: none;"><EMAIL></a>
                </p>

                <div style="margin-bottom: 2rem;">
                    <a href="https://www.facebook.com/audioexcellencesa/" target="_blank" rel="noopener" aria-label="Facebook" style="color: var(--accent-copper); font-size: 1.75rem; margin: 0 1.5rem; transition: var(--transition);">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://www.instagram.com/excellenceaudio/" target="_blank" rel="noopener" aria-label="Instagram" style="color: var(--accent-copper); font-size: 1.75rem; margin: 0 1.5rem; transition: var(--transition);">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>

                <p style="color: var(--light-gray); margin-top: 2rem; font-size: 0.95rem;">
                    © 2025 Audio Excellence Pty Ltd. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Product Detail Modal -->
    <div class="product-modal" id="productModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle" style="font-family: var(--font-display); color: var(--deep-charcoal); margin: 0;"></h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div>
                    <img id="modalImage" class="modal-image" src="" alt="">
                    <div class="modal-specs" id="modalSpecs">
                        <!-- Specs will be populated by JavaScript -->
                    </div>
                </div>
                <div>
                    <div id="modalBrand" style="color: var(--accent-copper); font-weight: 600; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 1rem;"></div>
                    <div id="modalPrice" style="font-family: var(--font-display); font-size: 2rem; font-weight: 700; color: var(--deep-charcoal); margin-bottom: 1.5rem;"></div>
                    <div id="modalDescription" style="color: var(--medium-gray); line-height: 1.6; margin-bottom: 2rem;"></div>
                    <div style="display: flex; gap: 1rem;">
                        <button class="btn-primary-store" style="flex: 1;">
                            <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                        </button>
                        <button class="wishlist-btn" style="padding: 0.6rem 1rem; border: 2px solid var(--accent-copper); border-radius: 8px; background: transparent;">
                            <i class="far fa-heart" style="color: var(--accent-copper);"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.luxury-navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Product data for modal
        const productData = {
            'McIntosh MA352 Hybrid Integrated Amplifier': {
                brand: 'McIntosh',
                price: 'R 189,900',
                image: '../assets/build2.png',
                description: 'The perfect marriage of vacuum tube warmth and solid state control. 200 watts per channel of pure McIntosh magic with legendary blue meters and handcrafted American excellence.',
                specs: {
                    'Power Output': '200W per channel',
                    'Tube Complement': '2 x 12AX7A, 2 x 12AT7',
                    'Frequency Response': '20Hz - 20kHz ±0.5dB',
                    'THD': '0.005%',
                    'Inputs': '6 Analog, 3 Digital',
                    'Dimensions': '44.5 x 24.1 x 55.9 cm',
                    'Weight': '36.3 kg'
                }
            },
            'KEF R11 Meta Floorstanding Speakers': {
                brand: 'KEF',
                price: 'R 84,900',
                image: '../assets/Home-White-Speakers-Gold-Rings-sml.jpg',
                description: 'Revolutionary Metamaterial Absorption Technology delivers the purest possible sound from the innovative Uni-Q driver array. Experience music as the artist intended.',
                specs: {
                    'Driver Configuration': '3-way, 4-driver',
                    'Frequency Response': '38Hz - 35kHz ±3dB',
                    'Sensitivity': '90dB',
                    'Impedance': '8Ω (3.2Ω min)',
                    'Crossover': '500Hz, 2.5kHz',
                    'Dimensions': '103.2 x 20 x 35.4 cm',
                    'Weight': '26.4 kg each'
                }
            },
            'Heritage Forte IV Speakers': {
                brand: 'Klipsch',
                price: 'R 67,500',
                image: '../assets/build1.png',
                description: 'Hand-crafted in Arkansas, these legendary speakers deliver the live music experience with unmatched dynamics and presence. Heritage series excellence.',
                specs: {
                    'Driver Configuration': '3-way horn-loaded',
                    'Frequency Response': '38Hz - 20kHz ±4dB',
                    'Sensitivity': '99dB',
                    'Power Handling': '100W RMS / 400W Peak',
                    'Impedance': '8Ω',
                    'Dimensions': '99.1 x 39.4 x 43.2 cm',
                    'Weight': '43.1 kg each'
                }
            },
            'M33 BluOS Streaming DAC Amplifier': {
                brand: 'NAD',
                price: 'R 89,900',
                image: '../assets/audio-system.jpeg',
                description: 'Revolutionary Eigentakt amplification meets high-resolution streaming. The future of integrated amplification with BluOS streaming platform.',
                specs: {
                    'Power Output': '200W per channel (4Ω)',
                    'Streaming': 'BluOS, Spotify Connect, Tidal',
                    'DAC': 'ESS Sabre32',
                    'Frequency Response': '20Hz - 20kHz ±0.1dB',
                    'THD': '<0.03%',
                    'Inputs': '2 Analog, 4 Digital, Network',
                    'Dimensions': '43.5 x 10 x 36.8 cm'
                }
            },
            'Debut Carbon EVO Turntable': {
                brand: 'Pro-Ject',
                price: 'R 12,900',
                image: 'placeholder', // Needs actual turntable image
                description: 'Precision-engineered turntable with carbon fiber tonearm and Ortofon 2M Red cartridge. Vinyl playback perfected with Austrian engineering.',
                specs: {
                    'Drive System': 'Belt Drive',
                    'Platter': 'Steel with TPE damping',
                    'Tonearm': 'Carbon fiber 8.6"',
                    'Cartridge': 'Ortofon 2M Red',
                    'Speed': '33⅓ & 45 RPM',
                    'Wow & Flutter': '<0.15%',
                    'Dimensions': '41.5 x 11.5 x 32 cm'
                }
            }
        };

        // Modal functions
        function openModal(productTitle) {
            const modal = document.getElementById('productModal');
            const data = productData[productTitle];

            if (data) {
                document.getElementById('modalTitle').textContent = productTitle;
                document.getElementById('modalBrand').textContent = data.brand;
                document.getElementById('modalPrice').textContent = data.price;
                document.getElementById('modalImage').src = data.image;
                document.getElementById('modalDescription').textContent = data.description;

                // Populate specs
                const specsContainer = document.getElementById('modalSpecs');
                specsContainer.innerHTML = '<h4 style="margin-bottom: 1rem; color: var(--deep-charcoal);">Specifications</h4>';

                Object.entries(data.specs).forEach(([key, value]) => {
                    specsContainer.innerHTML += `
                        <div class="spec-row">
                            <span class="spec-label">${key}:</span>
                            <span class="spec-value">${value}</span>
                        </div>
                    `;
                });

                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal() {
            const modal = document.getElementById('productModal');
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside
        document.getElementById('productModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Product filtering
        document.addEventListener('DOMContentLoaded', function() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            const productCards = document.querySelectorAll('.product-card');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');

                    // Update active button
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // Filter products
                    productCards.forEach(card => {
                        const cardCategories = card.getAttribute('data-category');

                        if (category === 'all' || cardCategories.includes(category)) {
                            card.style.display = 'block';
                            card.style.animation = 'fadeInUp 0.6s ease-out';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });

            // Product detail buttons
            const detailButtons = document.querySelectorAll('.btn-secondary-store');
            detailButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productCard = this.closest('.product-card');
                    const productTitle = productCard.querySelector('.product-title').textContent;
                    openModal(productTitle);
                });
            });

            // Wishlist functionality
            const wishlistButtons = document.querySelectorAll('.wishlist-btn');
            wishlistButtons.forEach(button => {
                button.addEventListener('click', function() {
                    this.classList.toggle('active');
                    const icon = this.querySelector('i');
                    if (this.classList.contains('active')) {
                        icon.classList.remove('far');
                        icon.classList.add('fas');
                    } else {
                        icon.classList.remove('fas');
                        icon.classList.add('far');
                    }
                });
            });

            // Add to cart functionality
            const addToCartButtons = document.querySelectorAll('.btn-primary-store');
            addToCartButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const productCard = this.closest('.product-card');
                    const productTitle = productCard.querySelector('.product-title').textContent;

                    // Visual feedback
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-check me-2"></i>Added!';
                    this.style.background = '#28a745';

                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.style.background = '';
                    }, 2000);

                    // You can add actual cart functionality here
                    console.log('Added to cart:', productTitle);
                });
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading animation
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in-out';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>

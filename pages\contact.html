<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact – Audio Excellence</title>
    <meta name="description" content="Contact Audio Excellence for high-end audio, home theatre, and automation solutions. Drop us a line or visit our showroom.">
    <link rel="icon" type="image/png" href="../assets/logo-full-transparent.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700;800;900&family=Helvetica+Neue:wght@300;400;500;600;700&family=Avenir:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            /* Luxury Color Palette */
            --primary-black: #000000;
            --primary-white: #ffffff;
            --luxury-cream: #f8f6f0;
            --warm-white: #fafafa;
            --accent-copper: #d4a574;
            --accent-gold: #c9a96e;
            --deep-charcoal: #1d1d1f;
            --medium-gray: #86868b;
            --light-gray: #f5f5f7;
            --glass-white: rgba(255, 255, 255, 0.8);
            
            /* Effects */
            --shadow-luxury: 0 8px 32px rgba(0, 0, 0, 0.12);
            --shadow-copper: 0 4px 20px rgba(212, 165, 116, 0.3);
            --shadow-deep: 0 16px 64px rgba(0, 0, 0, 0.15);
            --border-radius: 12px;
            --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            
            /* Typography */
            --font-primary: 'SF Pro Display', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-display: 'Playfair Display', serif;
        }
        
        body {
            font-family: var(--font-primary);
            background: var(--warm-white);
            color: var(--deep-charcoal);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
        }
        
        .luxury-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: var(--glass-white);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: var(--transition);
            padding: 0.5rem 0;
        }

        .luxury-navbar.scrolled {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-luxury);
        }

        .navbar-brand {
            font-family: var(--font-display);
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--deep-charcoal) !important;
            text-decoration: none;
            letter-spacing: -0.02em;
        }

        .navbar-nav .nav-link {
            color: var(--deep-charcoal) !important;
            font-weight: 500;
            font-size: 1rem;
            padding: 0.75rem 1.5rem !important;
            border-radius: var(--border-radius);
            transition: var(--transition);
            position: relative;
            margin: 0 0.25rem;
        }

        .navbar-nav .nav-link:hover {
            color: var(--accent-copper) !important;
            background: rgba(212, 165, 116, 0.1);
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            color: var(--accent-copper) !important;
            background: rgba(212, 165, 116, 0.15);
            font-weight: 600;
        }

        .navbar-toggler {
            border: none;
            padding: 0.25rem 0.5rem;
        }

        .navbar-toggler:focus {
            box-shadow: none;
        }

        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2829, 29, 31, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }
        
        .luxury-section {
            padding: 8rem 0;
            background: var(--luxury-cream);
        }
        
        .luxury-section:nth-child(even) {
            background: var(--warm-white);
        }
        
        .section-title {
            font-family: var(--font-display);
            font-size: clamp(2.5rem, 5vw, 4rem);
            color: var(--deep-charcoal);
            text-align: center;
            margin-bottom: 4rem;
            font-weight: 300;
            letter-spacing: -0.02em;
        }
        
        .section-title .accent {
            color: var(--accent-copper);
            font-weight: 600;
        }
        
        .luxury-card {
            background: var(--primary-white);
            border: 1px solid rgba(212, 165, 116, 0.2);
            border-radius: var(--border-radius);
            padding: 3rem 2.5rem;
            transition: var(--transition);
            height: 100%;
            box-shadow: var(--shadow-luxury);
        }
        
        .contact-info-card {
            background: var(--primary-white);
            border: 1px solid rgba(212, 165, 116, 0.2);
            border-radius: var(--border-radius);
            padding: 2.5rem;
            box-shadow: var(--shadow-luxury);
            margin-bottom: 2rem;
        }
        
        .form-control {
            border: 1px solid rgba(212, 165, 116, 0.3);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .form-control:focus {
            border-color: var(--accent-copper);
            box-shadow: 0 0 0 0.2rem rgba(212, 165, 116, 0.25);
        }
        
        .cta-button {
            background: linear-gradient(135deg, var(--accent-copper), var(--accent-gold));
            color: var(--primary-white) !important;
            padding: 0.75rem 2rem !important;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            box-shadow: var(--shadow-copper);
            border: none;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-deep);
            color: var(--primary-white) !important;
            background: linear-gradient(135deg, var(--accent-gold), var(--accent-copper));
        }
        
        .luxury-footer {
            background: var(--deep-charcoal);
            padding: 4rem 0 3rem;
            text-align: center;
            border-top: 1px solid rgba(212, 165, 116, 0.2);
        }
        
        .contact-icon {
            color: var(--accent-copper);
            margin-right: 1rem;
            font-size: 1.2rem;
        }
        
        .map-container {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-luxury);
            border: 1px solid rgba(212, 165, 116, 0.2);
        }

        /* Interactive Quote Builder Styles */
        .quote-builder-container {
            max-width: 1000px;
            margin: 0 auto;
            background: var(--primary-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            border: 1px solid rgba(212, 165, 116, 0.1);
        }

        .quote-progress {
            background: linear-gradient(135deg, var(--deep-charcoal), var(--medium-gray));
            padding: 2rem;
            color: var(--primary-white);
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.2);
            border-radius: 2px;
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-copper), var(--accent-gold));
            width: 25%;
            transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 2px;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .step.active {
            opacity: 1;
            transform: scale(1.1);
        }

        .step.completed {
            opacity: 0.8;
        }

        .step-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .step.active .step-icon {
            background: var(--accent-copper);
            box-shadow: 0 0 20px rgba(212, 165, 116, 0.5);
        }

        .step span {
            font-size: 0.9rem;
            font-weight: 500;
        }

        .quote-form-container {
            padding: 3rem;
        }

        .quote-step {
            display: none;
            animation: fadeInUp 0.6s ease;
        }

        .quote-step.active {
            display: block;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .step-title {
            font-family: var(--font-display);
            font-size: 2.2rem;
            color: var(--deep-charcoal);
            margin-bottom: 0.5rem;
            text-align: center;
        }

        .step-subtitle {
            color: var(--medium-gray);
            text-align: center;
            margin-bottom: 3rem;
            font-size: 1.1rem;
        }

        /* Space Options */
        .space-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .space-option {
            background: var(--primary-white);
            border: 2px solid var(--light-gray);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .space-option:hover {
            border-color: var(--accent-copper);
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(212, 165, 116, 0.2);
        }

        .space-option.selected {
            border-color: var(--accent-copper);
            background: linear-gradient(135deg, var(--primary-white), rgba(212, 165, 116, 0.05));
            box-shadow: 0 10px 30px rgba(212, 165, 116, 0.3);
        }

        .space-icon {
            font-size: 3rem;
            color: var(--accent-copper);
            margin-bottom: 1rem;
        }

        .space-option h4 {
            color: var(--deep-charcoal);
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .space-option p {
            color: var(--medium-gray);
            margin-bottom: 1.5rem;
        }

        .sound-wave {
            display: flex;
            justify-content: center;
            gap: 3px;
            height: 20px;
            align-items: end;
        }

        .wave {
            width: 3px;
            background: var(--accent-copper);
            border-radius: 2px;
            animation: wave 1.5s ease-in-out infinite;
        }

        .wave:nth-child(1) { animation-delay: 0s; height: 10px; }
        .wave:nth-child(2) { animation-delay: 0.2s; height: 15px; }
        .wave:nth-child(3) { animation-delay: 0.4s; height: 20px; }

        @keyframes wave {
            0%, 100% { transform: scaleY(1); }
            50% { transform: scaleY(0.3); }
        }

        .space-option:hover .wave {
            animation-duration: 0.8s;
        }

        /* Sound Preferences */
        .sound-preferences {
            margin-bottom: 3rem;
        }

        .preference-category {
            margin-bottom: 3rem;
        }

        .preference-category h4 {
            color: var(--deep-charcoal);
            margin-bottom: 1.5rem;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .preference-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .preference-item {
            position: relative;
            cursor: pointer;
            display: block;
        }

        .preference-item input[type="checkbox"] {
            display: none;
        }

        .checkmark {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 20px;
            height: 20px;
            border: 2px solid var(--light-gray);
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .preference-item input:checked + .checkmark {
            background: var(--accent-copper);
            border-color: var(--accent-copper);
        }

        .preference-item input:checked + .checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .preference-content {
            background: var(--primary-white);
            border: 2px solid var(--light-gray);
            border-radius: 12px;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .preference-item:hover .preference-content {
            border-color: var(--accent-copper);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(212, 165, 116, 0.15);
        }

        .preference-item input:checked + .checkmark + .preference-content {
            border-color: var(--accent-copper);
            background: linear-gradient(135deg, var(--primary-white), rgba(212, 165, 116, 0.05));
        }

        .preference-content i {
            font-size: 1.5rem;
            color: var(--accent-copper);
        }

        .preference-content span {
            color: var(--deep-charcoal);
            font-weight: 500;
        }

        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .feature-category h4 {
            color: var(--deep-charcoal);
            margin-bottom: 1.5rem;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .feature-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .feature-item {
            position: relative;
            cursor: pointer;
            display: block;
        }

        .feature-item input[type="checkbox"] {
            display: none;
        }

        .feature-check {
            position: absolute;
            top: 15px;
            left: 15px;
            width: 18px;
            height: 18px;
            border: 2px solid var(--light-gray);
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .feature-item input:checked + .feature-check {
            background: var(--accent-copper);
            border-color: var(--accent-copper);
        }

        .feature-item input:checked + .feature-check::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 10px;
            font-weight: bold;
        }

        .feature-content {
            background: var(--primary-white);
            border: 2px solid var(--light-gray);
            border-radius: 12px;
            padding: 1.5rem 1.5rem 1.5rem 3.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .feature-item:hover .feature-content {
            border-color: var(--accent-copper);
            transform: translateX(5px);
            box-shadow: 0 5px 20px rgba(212, 165, 116, 0.15);
        }

        .feature-item input:checked + .feature-check + .feature-content {
            border-color: var(--accent-copper);
            background: linear-gradient(135deg, var(--primary-white), rgba(212, 165, 116, 0.05));
        }

        .feature-content i {
            font-size: 1.8rem;
            color: var(--accent-copper);
            min-width: 40px;
        }

        .feature-content strong {
            color: var(--deep-charcoal);
            display: block;
            margin-bottom: 0.3rem;
        }

        .feature-content p {
            color: var(--medium-gray);
            margin: 0;
            font-size: 0.9rem;
        }

        /* Sound Sliders */
        .sound-sliders {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .slider-group {
            background: var(--primary-white);
            border: 2px solid var(--light-gray);
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .slider-group:hover {
            border-color: var(--accent-copper);
            box-shadow: 0 5px 20px rgba(212, 165, 116, 0.1);
        }

        .slider-group label {
            color: var(--deep-charcoal);
            font-weight: 600;
            margin-bottom: 1rem;
            display: block;
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .slider-container span {
            color: var(--medium-gray);
            font-size: 0.9rem;
            min-width: 60px;
            text-align: center;
        }

        .sound-slider {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: var(--light-gray);
            outline: none;
            -webkit-appearance: none;
        }

        .sound-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--accent-copper);
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(212, 165, 116, 0.3);
            transition: all 0.3s ease;
        }

        .sound-slider::-webkit-slider-thumb:hover {
            transform: scale(1.2);
            box-shadow: 0 4px 15px rgba(212, 165, 116, 0.5);
        }

        .sound-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--accent-copper);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 10px rgba(212, 165, 116, 0.3);
        }

        /* Contact Form Elegant */
        .contact-form-elegant {
            max-width: 600px;
            margin: 0 auto;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .form-group {
            position: relative;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
            margin-bottom: 2rem;
        }

        .form-group label {
            position: absolute;
            top: 0;
            left: 0;
            color: var(--medium-gray);
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 1rem 0 0.5rem 0;
            border: none;
            background: transparent;
            color: var(--deep-charcoal);
            font-size: 1rem;
            outline: none;
        }

        .form-group input:focus + .form-line,
        .form-group textarea:focus + .form-line,
        .form-group input:valid + .form-line,
        .form-group textarea:valid + .form-line {
            background: var(--accent-copper);
            transform: scaleX(1);
        }

        .form-group input:focus ~ label,
        .form-group textarea:focus ~ label,
        .form-group input:valid ~ label,
        .form-group textarea:valid ~ label {
            transform: translateY(-1.2rem) scale(0.8);
            color: var(--accent-copper);
        }

        .form-line {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--light-gray);
            transform-origin: center;
            transition: all 0.3s ease;
        }

        .form-line::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--accent-copper);
            transform: scaleX(0);
            transform-origin: center;
            transition: transform 0.3s ease;
        }

        .form-group input:focus + .form-line::after,
        .form-group textarea:focus + .form-line::after {
            transform: scaleX(1);
        }

        /* Consultation Options */
        .consultation-options {
            margin: 3rem 0;
        }

        .consultation-options h4 {
            color: var(--deep-charcoal);
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .consultation-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }

        .consultation-option {
            cursor: pointer;
            display: block;
        }

        .consultation-option input[type="radio"] {
            display: none;
        }

        .consultation-card {
            background: var(--primary-white);
            border: 2px solid var(--light-gray);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .consultation-option:hover .consultation-card {
            border-color: var(--accent-copper);
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(212, 165, 116, 0.15);
        }

        .consultation-option input:checked + .consultation-card {
            border-color: var(--accent-copper);
            background: linear-gradient(135deg, var(--primary-white), rgba(212, 165, 116, 0.05));
            box-shadow: 0 8px 25px rgba(212, 165, 116, 0.2);
        }

        .consultation-card i {
            font-size: 2rem;
            color: var(--accent-copper);
            margin-bottom: 1rem;
        }

        .consultation-card h5 {
            color: var(--deep-charcoal);
            margin-bottom: 0.5rem;
        }

        .consultation-card p {
            color: var(--medium-gray);
            margin: 0;
            font-size: 0.9rem;
        }

        /* Submit Button */
        .submit-quote-btn {
            width: 100%;
            background: linear-gradient(135deg, var(--accent-copper), var(--accent-gold));
            border: none;
            border-radius: 50px;
            padding: 1.2rem 2rem;
            color: var(--primary-white);
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            margin-top: 2rem;
        }

        .submit-quote-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(212, 165, 116, 0.4);
        }

        .submit-quote-btn:active {
            transform: translateY(0);
        }

        .btn-text {
            position: relative;
            z-index: 2;
        }

        .btn-sound-wave {
            position: absolute;
            right: 2rem;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 2px;
            z-index: 2;
        }

        .btn-sound-wave .wave {
            width: 2px;
            background: rgba(255,255,255,0.7);
            border-radius: 1px;
            animation: btnWave 1.2s ease-in-out infinite;
        }

        .btn-sound-wave .wave:nth-child(1) { height: 8px; animation-delay: 0s; }
        .btn-sound-wave .wave:nth-child(2) { height: 12px; animation-delay: 0.1s; }
        .btn-sound-wave .wave:nth-child(3) { height: 16px; animation-delay: 0.2s; }
        .btn-sound-wave .wave:nth-child(4) { height: 12px; animation-delay: 0.3s; }

        @keyframes btnWave {
            0%, 100% { transform: scaleY(1); opacity: 0.7; }
            50% { transform: scaleY(0.3); opacity: 1; }
        }

        /* Navigation Buttons */
        .quote-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid var(--light-gray);
        }

        .nav-btn {
            background: var(--primary-white);
            border: 2px solid var(--accent-copper);
            border-radius: 50px;
            padding: 0.8rem 2rem;
            color: var(--accent-copper);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-btn:hover {
            background: var(--accent-copper);
            color: var(--primary-white);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(212, 165, 116, 0.3);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .next-btn {
            margin-left: auto;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .features-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .consultation-grid {
                grid-template-columns: 1fr;
            }

            .space-options {
                grid-template-columns: 1fr;
            }

            .preference-grid {
                grid-template-columns: 1fr;
            }

            .progress-steps {
                flex-wrap: wrap;
                gap: 1rem;
            }

            .step {
                flex: 1;
                min-width: 80px;
            }

            .quote-form-container {
                padding: 2rem 1.5rem;
            }
        }
    </style>
</head>

<body>
    <!-- Modern Navigation -->
    <nav class="navbar navbar-expand-lg luxury-navbar fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../index.html">
                <img src="../assets/logo-full-transparent.png" alt="Audio Excellence Logo" style="height: 2.5rem; width: auto; max-width: 220px; object-fit: contain; display: inline-block; vertical-align: middle;" />
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./music-systems.html">Music Systems</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./home-theatre.html">Home Theatre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./home-automation.html">Home Automation</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="./showcase.html">Showcase</a>
                    </li>
                </ul>
                <a href="./contact.html" class="cta-button ms-3">Book a Demo</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="luxury-section" style="margin-top: 80px; background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.6)), url('../assets/contactbg.webp'); background-size: cover; background-position: center; background-attachment: fixed; min-height: 60vh; display: flex; align-items: center;">
        <div class="container text-center">
            <h1 class="section-title" data-aos="fade-up" style="margin-bottom: 1rem; color: var(--primary-white);">Drop Us a <span class="accent">Line</span></h1>
            <p class="lead" data-aos="fade-up" data-aos-delay="200" style="font-size: 1.4rem; color: var(--primary-white); max-width: 700px; margin: 0 auto; line-height: 1.6;">We'd love to hear from you. Reach out for expert advice, a demo, or to start your project.</p>
        </div>
    </section>

    <!-- Interactive Quote Builder -->
    <section class="luxury-section" style="background: linear-gradient(135deg, var(--luxury-cream), var(--warm-white));">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">Design Your Perfect <span class="accent">Audio Experience</span></h2>
                <p class="lead" style="color: var(--medium-gray); max-width: 700px; margin: 0 auto;">
                    Let us craft a personalized audio solution that transforms your space into an acoustic masterpiece.
                </p>
            </div>

            <div class="quote-builder-container" data-aos="fade-up" data-aos-delay="200">
                <div class="quote-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-steps">
                        <div class="step active" data-step="1">
                            <div class="step-icon"><i class="fas fa-home"></i></div>
                            <span>Space</span>
                        </div>
                        <div class="step" data-step="2">
                            <div class="step-icon"><i class="fas fa-music"></i></div>
                            <span>Sound</span>
                        </div>
                        <div class="step" data-step="3">
                            <div class="step-icon"><i class="fas fa-sliders"></i></div>
                            <span>Preferences</span>
                        </div>
                        <div class="step" data-step="4">
                            <div class="step-icon"><i class="fas fa-user"></i></div>
                            <span>Details</span>
                        </div>
                    </div>
                </div>

                <div class="quote-form-container">
                    <!-- Step 1: Space Type -->
                    <div class="quote-step active" id="step1">
                        <h3 class="step-title">What space are we designing for?</h3>
                        <p class="step-subtitle">Each environment has unique acoustic characteristics</p>

                        <div class="space-options">
                            <div class="space-option" data-value="living-room">
                                <div class="space-icon">
                                    <i class="fas fa-couch"></i>
                                </div>
                                <h4>Living Room</h4>
                                <p>Open, social spaces for everyday listening</p>
                                <div class="sound-wave">
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                </div>
                            </div>

                            <div class="space-option" data-value="dedicated-theatre">
                                <div class="space-icon">
                                    <i class="fas fa-film"></i>
                                </div>
                                <h4>Dedicated Theatre</h4>
                                <p>Purpose-built cinema experience</p>
                                <div class="sound-wave">
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                </div>
                            </div>

                            <div class="space-option" data-value="bedroom">
                                <div class="space-icon">
                                    <i class="fas fa-bed"></i>
                                </div>
                                <h4>Bedroom</h4>
                                <p>Intimate, personal listening spaces</p>
                                <div class="sound-wave">
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                </div>
                            </div>

                            <div class="space-option" data-value="office">
                                <div class="space-icon">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <h4>Office/Study</h4>
                                <p>Focused environments for concentration</p>
                                <div class="sound-wave">
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                </div>
                            </div>

                            <div class="space-option" data-value="whole-home">
                                <div class="space-icon">
                                    <i class="fas fa-house-signal"></i>
                                </div>
                                <h4>Whole Home</h4>
                                <p>Multi-room integrated systems</p>
                                <div class="sound-wave">
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                </div>
                            </div>

                            <div class="space-option" data-value="outdoor">
                                <div class="space-icon">
                                    <i class="fas fa-tree"></i>
                                </div>
                                <h4>Outdoor Space</h4>
                                <p>Patios, gardens, and entertainment areas</p>
                                <div class="sound-wave">
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Sound Preferences -->
                    <div class="quote-step" id="step2">
                        <h3 class="step-title">What moves your soul?</h3>
                        <p class="step-subtitle">Your musical preferences shape our recommendations</p>

                        <div class="sound-preferences">
                            <div class="preference-category">
                                <h4><i class="fas fa-music"></i> Primary Music Genres</h4>
                                <div class="preference-grid">
                                    <label class="preference-item">
                                        <input type="checkbox" name="genres" value="classical">
                                        <span class="checkmark"></span>
                                        <div class="preference-content">
                                            <i class="fas fa-violin"></i>
                                            <span>Classical & Orchestral</span>
                                        </div>
                                    </label>

                                    <label class="preference-item">
                                        <input type="checkbox" name="genres" value="jazz">
                                        <span class="checkmark"></span>
                                        <div class="preference-content">
                                            <i class="fas fa-saxophone"></i>
                                            <span>Jazz & Blues</span>
                                        </div>
                                    </label>

                                    <label class="preference-item">
                                        <input type="checkbox" name="genres" value="rock">
                                        <span class="checkmark"></span>
                                        <div class="preference-content">
                                            <i class="fas fa-guitar"></i>
                                            <span>Rock & Alternative</span>
                                        </div>
                                    </label>

                                    <label class="preference-item">
                                        <input type="checkbox" name="genres" value="electronic">
                                        <span class="checkmark"></span>
                                        <div class="preference-content">
                                            <i class="fas fa-wave-square"></i>
                                            <span>Electronic & EDM</span>
                                        </div>
                                    </label>

                                    <label class="preference-item">
                                        <input type="checkbox" name="genres" value="vocal">
                                        <span class="checkmark"></span>
                                        <div class="preference-content">
                                            <i class="fas fa-microphone"></i>
                                            <span>Vocal & Acoustic</span>
                                        </div>
                                    </label>

                                    <label class="preference-item">
                                        <input type="checkbox" name="genres" value="world">
                                        <span class="checkmark"></span>
                                        <div class="preference-content">
                                            <i class="fas fa-globe"></i>
                                            <span>World & Folk</span>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <div class="preference-category">
                                <h4><i class="fas fa-film"></i> Entertainment Preferences</h4>
                                <div class="preference-grid">
                                    <label class="preference-item">
                                        <input type="checkbox" name="entertainment" value="movies">
                                        <span class="checkmark"></span>
                                        <div class="preference-content">
                                            <i class="fas fa-video"></i>
                                            <span>Movies & Cinema</span>
                                        </div>
                                    </label>

                                    <label class="preference-item">
                                        <input type="checkbox" name="entertainment" value="gaming">
                                        <span class="checkmark"></span>
                                        <div class="preference-content">
                                            <i class="fas fa-gamepad"></i>
                                            <span>Gaming</span>
                                        </div>
                                    </label>

                                    <label class="preference-item">
                                        <input type="checkbox" name="entertainment" value="streaming">
                                        <span class="checkmark"></span>
                                        <div class="preference-content">
                                            <i class="fas fa-tv"></i>
                                            <span>TV & Streaming</span>
                                        </div>
                                    </label>

                                    <label class="preference-item">
                                        <input type="checkbox" name="entertainment" value="vinyl">
                                        <span class="checkmark"></span>
                                        <div class="preference-content">
                                            <i class="fas fa-record-vinyl"></i>
                                            <span>Vinyl & Analog</span>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Preferences & Features -->
                    <div class="quote-step" id="step3">
                        <h3 class="step-title">What features matter most to you?</h3>
                        <p class="step-subtitle">Let's customize your perfect audio experience</p>

                        <div class="features-grid">
                            <div class="feature-category">
                                <h4><i class="fas fa-cog"></i> System Features</h4>
                                <div class="feature-options">
                                    <label class="feature-item">
                                        <input type="checkbox" name="features" value="wireless">
                                        <span class="feature-check"></span>
                                        <div class="feature-content">
                                            <i class="fas fa-wifi"></i>
                                            <div>
                                                <strong>Wireless Connectivity</strong>
                                                <p>Bluetooth, WiFi, and streaming integration</p>
                                            </div>
                                        </div>
                                    </label>

                                    <label class="feature-item">
                                        <input type="checkbox" name="features" value="automation">
                                        <span class="feature-check"></span>
                                        <div class="feature-content">
                                            <i class="fas fa-home"></i>
                                            <div>
                                                <strong>Smart Home Integration</strong>
                                                <p>Voice control and automated scenes</p>
                                            </div>
                                        </div>
                                    </label>

                                    <label class="feature-item">
                                        <input type="checkbox" name="features" value="multiroom">
                                        <span class="feature-check"></span>
                                        <div class="feature-content">
                                            <i class="fas fa-house-signal"></i>
                                            <div>
                                                <strong>Multi-Room Audio</strong>
                                                <p>Synchronized music throughout your home</p>
                                            </div>
                                        </div>
                                    </label>

                                    <label class="feature-item">
                                        <input type="checkbox" name="features" value="highres">
                                        <span class="feature-check"></span>
                                        <div class="feature-content">
                                            <i class="fas fa-gem"></i>
                                            <div>
                                                <strong>High-Resolution Audio</strong>
                                                <p>Studio-quality sound reproduction</p>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <div class="feature-category">
                                <h4><i class="fas fa-volume-up"></i> Sound Characteristics</h4>
                                <div class="sound-sliders">
                                    <div class="slider-group">
                                        <label>Bass Response</label>
                                        <div class="slider-container">
                                            <span>Tight</span>
                                            <input type="range" class="sound-slider" min="1" max="5" value="3" data-preference="bass">
                                            <span>Deep</span>
                                        </div>
                                    </div>

                                    <div class="slider-group">
                                        <label>Soundstage</label>
                                        <div class="slider-container">
                                            <span>Intimate</span>
                                            <input type="range" class="sound-slider" min="1" max="5" value="3" data-preference="soundstage">
                                            <span>Expansive</span>
                                        </div>
                                    </div>

                                    <div class="slider-group">
                                        <label>Detail Level</label>
                                        <div class="slider-container">
                                            <span>Smooth</span>
                                            <input type="range" class="sound-slider" min="1" max="5" value="3" data-preference="detail">
                                            <span>Analytical</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 4: Contact Details -->
                    <div class="quote-step" id="step4">
                        <h3 class="step-title">Let's connect and create magic</h3>
                        <p class="step-subtitle">We'll craft a personalized consultation just for you</p>

                        <div class="contact-form-elegant">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="quoteFirstName">First Name</label>
                                    <input type="text" id="quoteFirstName" name="firstName" required>
                                    <div class="form-line"></div>
                                </div>

                                <div class="form-group">
                                    <label for="quoteLastName">Last Name</label>
                                    <input type="text" id="quoteLastName" name="lastName" required>
                                    <div class="form-line"></div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="quoteEmail">Email Address</label>
                                    <input type="email" id="quoteEmail" name="email" required>
                                    <div class="form-line"></div>
                                </div>

                                <div class="form-group">
                                    <label for="quotePhone">Phone Number</label>
                                    <input type="tel" id="quotePhone" name="phone">
                                    <div class="form-line"></div>
                                </div>
                            </div>

                            <div class="form-group full-width">
                                <label for="quoteLocation">Project Location</label>
                                <input type="text" id="quoteLocation" name="location" placeholder="City, Area">
                                <div class="form-line"></div>
                            </div>

                            <div class="form-group full-width">
                                <label for="quoteNotes">Additional Notes</label>
                                <textarea id="quoteNotes" name="notes" rows="3" placeholder="Tell us about your vision, timeline, or any specific requirements..."></textarea>
                                <div class="form-line"></div>
                            </div>

                            <div class="consultation-options">
                                <h4>Preferred Consultation Method</h4>
                                <div class="consultation-grid">
                                    <label class="consultation-option">
                                        <input type="radio" name="consultationType" value="showroom" checked>
                                        <div class="consultation-card">
                                            <i class="fas fa-store"></i>
                                            <h5>Showroom Visit</h5>
                                            <p>Experience our reference systems</p>
                                        </div>
                                    </label>

                                    <label class="consultation-option">
                                        <input type="radio" name="consultationType" value="home">
                                        <div class="consultation-card">
                                            <i class="fas fa-home"></i>
                                            <h5>Home Consultation</h5>
                                            <p>We'll assess your space personally</p>
                                        </div>
                                    </label>

                                    <label class="consultation-option">
                                        <input type="radio" name="consultationType" value="virtual">
                                        <div class="consultation-card">
                                            <i class="fas fa-video"></i>
                                            <h5>Virtual Meeting</h5>
                                            <p>Online consultation and planning</p>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="submit-quote-btn" id="submitQuote">
                                <span class="btn-text">Request My Consultation</span>
                                <div class="btn-sound-wave">
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="quote-navigation">
                        <button type="button" class="nav-btn prev-btn" id="prevBtn" style="display: none;">
                            <i class="fas fa-arrow-left"></i> Previous
                        </button>
                        <button type="button" class="nav-btn next-btn" id="nextBtn">
                            Next <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="luxury-section">
        <div class="container">
            <div class="row g-5">
                <div class="col-lg-5" data-aos="fade-right">
                    <div class="contact-info-card">
                        <h3 style="color: var(--accent-copper); margin-bottom: 2rem; font-family: var(--font-display);">Contact Information</h3>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <i class="fas fa-phone contact-icon"></i>
                            <a href="tel:+27315665931" style="color: var(--deep-charcoal); text-decoration: none; font-weight: 500;">(*************</a>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <i class="fas fa-envelope contact-icon"></i>
                            <a href="mailto:<EMAIL>" style="color: var(--deep-charcoal); text-decoration: none; font-weight: 500;"><EMAIL></a>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <i class="fas fa-location-dot contact-icon"></i>
                            <span style="color: var(--deep-charcoal); font-weight: 500;">Gateway Office Park, 1 Sugar Close, Umhlanga Ridge, 4320</span>
                        </div>
                        
                        <div style="margin-bottom: 2rem;">
                            <i class="fas fa-clock contact-icon"></i>
                            <div style="color: var(--deep-charcoal);">
                                <strong>Business Hours:</strong><br>
                                Mon–Fri: 9am–5pm<br>
                                Sat: 9am–1pm<br>
                                Sun & Public Holidays: Closed
                            </div>
                        </div>
                        
                        <div style="margin-top: 2rem;">
                            <a href="https://www.facebook.com/audioexcellencesa/" target="_blank" rel="noopener" aria-label="Facebook" style="color: var(--accent-copper); font-size: 1.5rem; margin-right: 1.5rem; transition: var(--transition);">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://www.instagram.com/excellenceaudio/" target="_blank" rel="noopener" aria-label="Instagram" style="color: var(--accent-copper); font-size: 1.5rem; transition: var(--transition);">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Map -->
                    <div class="map-container">
                        <div class="ratio ratio-16x9">
                            <iframe src="https://www.google.com/maps?q=Gateway+Office+Park,+1+Sugar+Close,+Umhlanga+Ridge,+4320&output=embed" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-7" data-aos="fade-left">
                    <div class="luxury-card">
                        <h3 style="color: var(--accent-copper); margin-bottom: 2rem; font-family: var(--font-display);">Send us a Message</h3>
                        
                        <form action="#" method="post" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="name" class="form-label" style="color: var(--deep-charcoal); font-weight: 500;">Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="invalid-feedback">Please enter your name.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label" style="color: var(--deep-charcoal); font-weight: 500;">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <div class="invalid-feedback">Please enter a valid email address.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="subject" class="form-label" style="color: var(--deep-charcoal); font-weight: 500;">Subject</label>
                                <input type="text" class="form-control" id="subject" name="subject" required>
                                <div class="invalid-feedback">Please enter a subject.</div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="message" class="form-label" style="color: var(--deep-charcoal); font-weight: 500;">Message</label>
                                <textarea class="form-control" id="message" name="message" rows="6" required></textarea>
                                <div class="invalid-feedback">Please enter your message.</div>
                            </div>
                            
                            <button type="submit" class="cta-button" style="font-size: 1.1rem; padding: 1rem 2.5rem;">Send Message</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="luxury-footer">
        <div class="container">
            <div style="max-width: 800px; margin: 0 auto;">
                <h3 style="font-family: var(--font-display); font-size: 2rem; color: var(--accent-copper); margin-bottom: 1.5rem; font-weight: 600;">Audio Excellence</h3>
                <p style="color: var(--light-gray); margin-bottom: 2rem; font-size: 1.1rem; line-height: 1.6;">
                    Gateway Office Park, 1 Sugar Close, Umhlanga Ridge, 4320<br>
                    <strong>(*************</strong> | 
                    <a href="mailto:<EMAIL>" style="color: var(--accent-copper); text-decoration: none;"><EMAIL></a>
                </p>
                
                <div style="margin-bottom: 2rem;">
                    <a href="https://facebook.com/audioexcellenceza" target="_blank" rel="noopener" aria-label="Facebook" style="color: var(--accent-copper); font-size: 1.75rem; margin: 0 1.5rem; transition: var(--transition);">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://instagram.com/audioexcellenceza" target="_blank" rel="noopener" aria-label="Instagram" style="color: var(--accent-copper); font-size: 1.75rem; margin: 0 1.5rem; transition: var(--transition);">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>
                
                <p style="color: var(--light-gray); margin-top: 2rem; font-size: 0.95rem;">
                    © 2025 Audio Excellence Pty Ltd. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.luxury-navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Bootstrap form validation
        (function() {
            'use strict';
            const forms = document.querySelectorAll('.needs-validation');
            Array.from(forms).forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        })();

        // Interactive Quote Builder
        class QuoteBuilder {
            constructor() {
                this.currentStep = 1;
                this.totalSteps = 4;
                this.formData = {};
                this.init();
            }

            init() {
                this.bindEvents();
                this.updateProgress();
            }

            bindEvents() {
                // Navigation buttons
                document.getElementById('nextBtn').addEventListener('click', () => this.nextStep());
                document.getElementById('prevBtn').addEventListener('click', () => this.prevStep());

                // Space selection
                document.querySelectorAll('.space-option').forEach(option => {
                    option.addEventListener('click', () => this.selectSpace(option));
                });

                // Form inputs
                document.querySelectorAll('input, textarea, select').forEach(input => {
                    input.addEventListener('change', () => this.saveFormData());
                });

                // Submit button
                document.getElementById('submitQuote').addEventListener('click', (e) => {
                    e.preventDefault();
                    this.submitQuote();
                });
            }

            selectSpace(option) {
                // Remove previous selection
                document.querySelectorAll('.space-option').forEach(opt => opt.classList.remove('selected'));

                // Add selection to clicked option
                option.classList.add('selected');

                // Save selection
                this.formData.spaceType = option.dataset.value;

                // Enable next button
                document.getElementById('nextBtn').disabled = false;

                // Auto-advance after a short delay
                setTimeout(() => {
                    if (this.currentStep === 1) {
                        this.nextStep();
                    }
                }, 800);
            }

            nextStep() {
                if (this.currentStep < this.totalSteps) {
                    // Hide current step
                    document.getElementById(`step${this.currentStep}`).classList.remove('active');

                    // Show next step
                    this.currentStep++;
                    document.getElementById(`step${this.currentStep}`).classList.add('active');

                    // Update progress
                    this.updateProgress();
                    this.updateNavigation();
                }
            }

            prevStep() {
                if (this.currentStep > 1) {
                    // Hide current step
                    document.getElementById(`step${this.currentStep}`).classList.remove('active');

                    // Show previous step
                    this.currentStep--;
                    document.getElementById(`step${this.currentStep}`).classList.add('active');

                    // Update progress
                    this.updateProgress();
                    this.updateNavigation();
                }
            }

            updateProgress() {
                const progressFill = document.getElementById('progressFill');
                const progressPercentage = (this.currentStep / this.totalSteps) * 100;
                progressFill.style.width = `${progressPercentage}%`;

                // Update step indicators
                document.querySelectorAll('.step').forEach((step, index) => {
                    const stepNumber = index + 1;
                    step.classList.remove('active', 'completed');

                    if (stepNumber === this.currentStep) {
                        step.classList.add('active');
                    } else if (stepNumber < this.currentStep) {
                        step.classList.add('completed');
                    }
                });
            }

            updateNavigation() {
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');

                // Show/hide previous button
                prevBtn.style.display = this.currentStep > 1 ? 'flex' : 'none';

                // Update next button text and visibility
                if (this.currentStep === this.totalSteps) {
                    nextBtn.style.display = 'none';
                } else {
                    nextBtn.style.display = 'flex';
                    nextBtn.innerHTML = 'Next <i class="fas fa-arrow-right"></i>';
                }
            }

            saveFormData() {
                // Save all form data
                const formElements = document.querySelectorAll('#step1 input, #step2 input, #step3 input, #step4 input, #step4 textarea');

                formElements.forEach(element => {
                    if (element.type === 'checkbox' || element.type === 'radio') {
                        if (element.checked) {
                            if (!this.formData[element.name]) {
                                this.formData[element.name] = [];
                            }
                            if (element.type === 'radio') {
                                this.formData[element.name] = element.value;
                            } else {
                                this.formData[element.name].push(element.value);
                            }
                        }
                    } else {
                        this.formData[element.name] = element.value;
                    }
                });
            }

            submitQuote() {
                this.saveFormData();

                // Create a beautiful success message
                const submitBtn = document.getElementById('submitQuote');
                const originalText = submitBtn.innerHTML;

                submitBtn.innerHTML = '<i class="fas fa-check"></i> Consultation Requested!';
                submitBtn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';

                // Simulate form submission
                setTimeout(() => {
                    alert(`Thank you ${this.formData.firstName || 'for your interest'}! We'll contact you within 24 hours to schedule your personalized consultation. Get ready to experience audio excellence like never before!`);

                    // Reset form
                    this.resetForm();
                }, 1000);
            }

            resetForm() {
                this.currentStep = 1;
                this.formData = {};

                // Reset all steps
                document.querySelectorAll('.quote-step').forEach(step => step.classList.remove('active'));
                document.getElementById('step1').classList.add('active');

                // Reset selections
                document.querySelectorAll('.space-option').forEach(opt => opt.classList.remove('selected'));
                document.querySelectorAll('input').forEach(input => {
                    if (input.type === 'checkbox' || input.type === 'radio') {
                        input.checked = false;
                    } else {
                        input.value = '';
                    }
                });

                // Reset progress
                this.updateProgress();
                this.updateNavigation();

                // Reset submit button
                const submitBtn = document.getElementById('submitQuote');
                submitBtn.innerHTML = '<span class="btn-text">Request My Consultation</span><div class="btn-sound-wave"><div class="wave"></div><div class="wave"></div><div class="wave"></div><div class="wave"></div></div>';
                submitBtn.style.background = 'linear-gradient(135deg, var(--accent-copper), var(--accent-gold))';
            }
        }

        // Initialize Quote Builder when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            new QuoteBuilder();
        });
    </script>
</body>
</html>
